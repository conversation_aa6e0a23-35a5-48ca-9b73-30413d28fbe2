import {configureStore} from '@reduxjs/toolkit';

// 加减示例
import sumSlice from './sumSlice';

// 全局权限
import globalAuthSlice from './GlobalAuth';

// 工作空间权限
import workspaceAuthSlice from './WorkspaceAuth';

// Notebook 状态
import notebookSlice from './notebookSlice';

// configureStore 创建一个 redux 数据
const store = configureStore({
  // 可合并多个Slice
  reducer: {
    sumSlice,
    globalAuthSlice,
    workspaceAuthSlice,
    notebookSlice
  }
});

export type IAppState = ReturnType<typeof store.getState>;
export type IAppDispatch = typeof store.dispatch;

export default store;
