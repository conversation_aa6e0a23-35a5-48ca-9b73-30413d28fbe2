import {BaseResponseType, request} from '@baidu/bce-react-toolkit';
import {JobInstanceOperateType} from '@pages/JobInstance/constants';
import {IJsonNodeData} from '@pages/JobWorkflow/Jobs/components/X6Page/type';
import {IQueryListParams} from '@type/common';
import {dealQueryParams} from '@utils/utils';

const apiUrl = '/api/databuilder/v1';
// 任务实例 的每个任务
export interface IJobInstanceTask {
  id: string;
  name: string;
  type: string;
  subTaskCode: string;
  taskStatus: string;
  computeId?: string;
  durationSec?: string;
  startTime?: string;
  endTime?: string;
  operatorIds?: string[];
}

// 合并 code 和 任务结果类型
export type IJobInstanceTaskNode = IJobInstanceTask & IJsonNodeData;

// 工作流 运行记录
export interface IJobInstance {
  workspaceId?: string;
  jobId?: string;
  jobInstanceId?: string;
  jobName?: string;
  status?: string; // 状态
  jobStatus?: string; // 状态
  durationSec?: number; // 调度时间
  runUsername?: string; // 运行用户
  runUserId?: string; // 运行用户 id
  startTime?: string; // 开始时间
  endTime?: string; // 结束时间
  code?: string; // 代码
  errorMsg?: string; // 错误信息
  taskDetailEntries?: IJobInstanceTask[];
}

// 重跑类型
export enum JobInstanceReRunType {
  // 对应失败点继续
  FROM_FAILURE = 'FROM_FAILURE',
  // 对应从头开始重跑
  FROM_FIRST = 'FROM_FIRST'
}

// 查询参数
export interface IQueryJobInstanceParams extends IQueryListParams {
  workspaceId?: string;
  // 工作流 id 逗号分割
  searchJobIds?: string[];
  // 状态 逗号分割
  searchStatus?: string[];
  // 用户 id 逗号分割
  searchUserIds?: string[];
  // 开始时间 范围 2025-02-28T16:00:00Z,2025-02-28T23:59:59Z
  startTimeTo?: string;
  startTimeFrom?: string;
  // 结束时间 范围 2025-02-28T16:00:00Z,2025-02-28T23:59:59Z
  endTimeTo?: string;
  endTimeFrom?: string;
}

/** 查询列表 */
export function queryJobInstanceList(
  sourceParams: IQueryJobInstanceParams,
  workspaceId: string
): BaseResponseType<{
  result: Array<IJobInstance>;
  totalCount: number;
}> {
  const params = dealQueryParams(sourceParams, true);
  return request({
    url: `${apiUrl}/workspaces/${workspaceId}/jobinstances/list`,
    method: 'post',
    data: params
  });
}

/** 详情 */
export function detailJobInstance(
  workspaceId?: string,
  jobInstanceId?: string
): BaseResponseType<IJobInstance> {
  return request({
    url: `${apiUrl}/workspaces/${workspaceId}/jobinstances/${jobInstanceId}/detail`,
    method: 'GET'
  });
}

/** 恢复 重跑 暂停 停止 */
export function operateJobInstance(
  workspaceId?: string,
  jobInstanceId?: string,
  type?: JobInstanceOperateType,
  data = {}
): BaseResponseType<{jobId: string}> {
  return request({
    url: `${apiUrl}/workspaces/${workspaceId}/jobinstances/${jobInstanceId}/${type}`,
    method: 'POST',
    data
  });
}

/** 删除 */
export function deleteJobInstance(
  workspaceId?: string,
  jobInstanceId?: string
): BaseResponseType<{jobInstanceId: string}> {
  return request({
    url: `${apiUrl}/workspaces/${workspaceId}/jobinstances/${jobInstanceId}`,
    method: 'DELETE'
  });
}

// 查询日志 传递参数
export interface IJobInstanceTaskLogParams {
  jobInstanceId?: string;
  taskId?: string;
  // subTaskCode 是后台拆分 task 的，一期一个 task只有一个subTaskCode，后期会有多个
  subTaskCode?: string;
  // pageNo 可选若不传，默认查询最后一页，也是最新的数据
  pageNo?: number;
}
// 日志响应
export interface IJobInstanceTaskLogResult {
  // 总日志页数
  totalLogPageCount: number;
  // 是否任务结束
  taskFinished: boolean;
  // 日志内容
  logContent: string;
}
/**
 * 日志请求参数
 */
export function jobInstanceTaskLog(
  workspaceId?: string,
  params?: IJobInstanceTaskLogParams
): BaseResponseType<IJobInstanceTaskLogResult> {
  return request({
    url: `${apiUrl}/workspaces/${workspaceId}/jobinstances/logs`,
    method: 'get',
    params
  });
}
// 获取用户列表
export function jobInstanceListUser(workspaceId?: string): BaseResponseType<IJobInstanceTaskLogResult> {
  return request({
    url: `${apiUrl}/workspaces/${workspaceId}/jobinstances/listuser`,
    method: 'get'
  });
}

// 任务结果
export interface IJobInstanceTaskOutputResult {
  operatorName?: string;
  operatorId?: string;
  outputPath?: string;
  outputFormat?: string;
  generateTime?: string;
}
// 获取 任务结果
export function jobInstanceTaskOutputResult(
  workspaceId?: string,
  params?: IJobInstanceTaskLogParams
): BaseResponseType<{operatorOutputs: IJobInstanceTaskOutputResult[]}> {
  return request({
    url: `${apiUrl}/workspaces/${workspaceId}/jobinstances/taskoutput`,
    method: 'get',
    params
  });
}
