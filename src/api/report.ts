/* eslint-disable @typescript-eslint/no-unused-vars */
import {BaseResponseType, request} from '@baidu/bce-react-toolkit';

import {OrderType} from '@/utils/enums';

export interface Report {
  createTime: string;
  endTime: string;
  invalidTime: string;
  opAccountName: string;
  reportId: number;
  reportName: string;
  reportType: string;
  reportTypeDesc: string;
  startTime: string;
  status: number;
  statusDesc: string;
}

export interface QueryReportListParams {
  page?: number;
  pageSize?: number;
  search?: string;
  reportType?: string | null;
  order?: OrderType;
  orderBy?: string;
}

/** 查询报表列表 */
export function queryReportList(
  params: QueryReportListParams
): BaseResponseType<{
  reports: Array<Report>;
  totalCount: number;
}> {
  return Promise.resolve({
    success: true,
    status: 200,
    result: {
      reports: [
        {
          createTime: '2024-01-12T07:33:12Z',
          endTime: '2024-01-12T16:00:00Z',
          invalidTime: '2024-02-11T07:33:12Z',
          opAccountName: 'root',
          reportId: ************,
          reportName: 'ar-0AvVK1',
          reportType: 'general',
          reportTypeDesc: '综合报表',
          startTime: '2023-12-28T16:00:00Z',
          status: 3,
          statusDesc: '生效中'
        },
        {
          createTime: '2024-01-12T07:30:47Z',
          endTime: '2024-01-12T16:00:00Z',
          invalidTime: '2024-02-11T07:30:47Z',
          opAccountName: 'root',
          reportId: ************,
          reportName: "ar-AE9ebId's",
          reportType: 'general',
          reportTypeDesc: '综合报表',
          startTime: '2023-12-28T16:00:00Z',
          status: 3,
          statusDesc: '生效中'
        },
        {
          createTime: '2024-01-12T07:30:14Z',
          endTime: '2024-01-12T16:00:00Z',
          invalidTime: '2024-02-11T07:30:14Z',
          opAccountName: 'root',
          reportId: ************,
          reportName: 'ar-AE9ebI',
          reportType: 'general',
          reportTypeDesc: '综合报表',
          startTime: '2023-12-28T16:00:00Z',
          status: 3,
          statusDesc: '生效中'
        },
        {
          createTime: '2024-01-12T07:29:24Z',
          endTime: '2024-01-12T16:00:00Z',
          invalidTime: '2024-02-11T07:29:24Z',
          opAccountName: 'root',
          reportId: ************,
          reportName: 'ar-59yAm6',
          reportType: 'general',
          reportTypeDesc: '综合报表',
          startTime: '2023-12-28T16:00:00Z',
          status: 3,
          statusDesc: '生效中'
        },
        {
          createTime: '2024-01-12T07:28:29Z',
          endTime: '2024-01-12T16:00:00Z',
          invalidTime: '2024-02-11T07:28:29Z',
          opAccountName: 'root',
          reportId: ************,
          reportName: 'ar-3djNku',
          reportType: 'general',
          reportTypeDesc: '综合报表',
          startTime: '2023-12-28T16:00:00Z',
          status: 3,
          statusDesc: '生效中'
        },
        {
          createTime: '2024-01-12T07:26:04Z',
          endTime: '2024-01-12T16:00:00Z',
          invalidTime: '2024-02-11T07:26:04Z',
          opAccountName: 'root',
          reportId: ************,
          reportName: 'ar-pU6iFa',
          reportType: 'general',
          reportTypeDesc: '综合报表',
          startTime: '2023-12-28T16:00:00Z',
          status: 3,
          statusDesc: '生效中'
        },
        {
          createTime: '2024-01-11T13:07:28Z',
          endTime: '2024-01-11T16:00:00Z',
          invalidTime: '2024-02-10T13:07:28Z',
          opAccountName: 'root',
          reportId: ************,
          reportName: 'ar-DKFKi1',
          reportType: 'general',
          reportTypeDesc: '综合报表',
          startTime: '2023-12-27T16:00:00Z',
          status: 3,
          statusDesc: '生效中'
        },
        {
          createTime: '2023-12-29T10:42:54Z',
          endTime: '2023-12-30T16:00:00Z',
          invalidTime: '2024-01-28T10:42:54Z',
          opAccountName: 'root',
          reportId: ************,
          reportName: 'ar-TCeq7T',
          reportType: 'general',
          reportTypeDesc: '综合报表',
          startTime: '2023-12-14T16:00:00Z',
          status: 3,
          statusDesc: '生效中'
        },
        {
          createTime: '2023-12-29T10:15:02Z',
          endTime: '2023-12-28T16:00:00Z',
          invalidTime: '2024-01-28T10:15:02Z',
          opAccountName: 'root',
          reportId: ************,
          reportName: 'ar-i2vUqI',
          reportType: 'analysis',
          reportTypeDesc: '风险分析报表',
          startTime: '2023-12-14T16:00:00Z',
          status: 3,
          statusDesc: '生效中'
        },
        {
          createTime: '2023-12-29T02:31:11Z',
          endTime: '2023-12-29T16:00:00Z',
          invalidTime: '2024-01-28T02:31:11Z',
          opAccountName: 'root',
          reportId: ************,
          reportName: 'ar-SmRyy9',
          reportType: 'general',
          reportTypeDesc: '综合报表',
          startTime: '2023-12-14T16:00:00Z',
          status: 3,
          statusDesc: '生效中'
        }
      ],
      totalCount: 45
    }
  });
  // return request({
  //   url: '/api/dbsc/v1/audit/report/list',
  //   method: 'GET',
  //   params
  // });
}

/** 获取报表类型列表 */
export function queryReportType(): BaseResponseType<{
  types: Array<{
    desc: string;
    name: string;
  }>;
}> {
  return Promise.resolve({
    success: true,
    status: 200,
    result: {
      types: [
        {
          desc: '综合报表',
          name: 'general'
        },
        {
          desc: '风险分析报表',
          name: 'analysis'
        },
        {
          desc: '等级保护报表',
          name: 'protect'
        },
        {
          desc: 'SOX-塞班斯报告',
          name: 'sox'
        },
        {
          desc: 'PCI-DSS合规报告',
          name: 'pci'
        }
      ]
    }
  });
  // return request({
  //   url: '/api/dbsc/v1/audit/report/type',
  //   method: 'GET'
  // });
}

export interface CreateAuditReportParams {
  /** 报表名称 */
  reportName?: string;
  /** 报表类型 */
  reportType?: string;
  /** 日志开始时间 */
  start?: string;
  /** 日志结束时间 */
  end?: string;
}

/** 创建报表 */
export function createAuditReport(
  params: CreateAuditReportParams
): BaseResponseType<{
  id: number;
}> {
  return request({
    url: '/api/dbsc/v1/audit/report',
    method: 'POST',
    data: params
  });
}

/** 下载报表 */
export function downloadReport(params: {
  reports: Array<number | string>;
}): BaseResponseType<{
  links: Array<string>;
}> {
  return request({
    url: '/api/dbsc/v1/audit/report/download-link',
    method: 'POST',
    data: params
  })
}
