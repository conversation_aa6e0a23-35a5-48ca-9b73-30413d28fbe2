import {request, BaseResponseType} from '@baidu/bce-react-toolkit';

// 实际 API 路径前缀，用于真实 API 调用
const urlPrefix = '/api/databuilder/v1';
const vpcUrlPrefix = '/api/network/v1';

// 计算实例类型
export enum Engine {
  // 常驻实例
  Ray = 'Ray',
  // 查询检索实例
  Doris = 'Doris'
}

interface GetComputeResourceListParams {
  workspaceId: string;
  pageNo?: number;
  pageSize?: number;
  keyword?: string;
  status?: string;
  order?: 'asc' | 'desc';
  orderBy?: string;
  // 计算实例类型
  engine?: Engine;
}

export interface ComputeResourceItem {
  workspaceId: string; // 工作空间id
  name: string; // 计算实例名字
  status: 'INIT' | 'CREATING' | 'BOOSTRAPPING' | 'RUNNING' | 'CREATED_FAILED' | 'DELETING' | 'DELETED';
  creator: string; // 创建者名字
  createdAt: string; // 创建时间
  computeId?: string; // 计算实例 id
  region?: string; // 地域
  runtime?: string; // 运行环境
  clusterType?: string; // 集群资源规格
  engine?: string; // 集群计算引擎
  accountId?: string; // 账户id
  updatedAt?: string; // 更新时间
}

export interface GetComputeResourceListResult {
  computes: ComputeResourceItem[];
  total: number;
}

export function getComputeResourceList(
  params: GetComputeResourceListParams
): BaseResponseType<GetComputeResourceListResult> {
  // 实际 API 调用
  return request({
    url: `${urlPrefix}/workspaces/${params.workspaceId}/instances`,
    method: 'GET',
    params
  });
}

interface CreateComputeResourceParams {
  name: string;
  workspaceId: string;
  region: string;
  chargingType: string;
  type: string;
  vpcId: string;
  availableZone: string;
  subnetId: string;
  engine: string;
  mirrorVersion: string;
  clusterType: string;
  nodeCnt: number;
}

export function createComputeResource(params: CreateComputeResourceParams): BaseResponseType<any> {
  const {workspaceId} = params;

  // 实际 API 调用
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/instances`,
    method: 'POST',
    data: params
  });
}

export function deleteComputeResource({
  workspaceId,
  computeId
}: {
  workspaceId: string;
  computeId: string;
}): BaseResponseType<any> {
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/instances/${computeId}`,
    method: 'DELETE'
  });
}

interface Vpc {
  vpcId: string;
  name: string;
  cidr: string;
  shortId: string;
  defaultVpc: boolean;
}

type GetVpcListResult = Vpc[];

export function getVpcList(): BaseResponseType<GetVpcListResult> {
  return request({
    url: `${vpcUrlPrefix}/vpcs`,
    method: 'POST'
  });
}

export interface Zone {
  zoneName: string;
  specs: {
    dataBuilderClusterType: string;
    spec: string;
    inventoryQuantity: number;
    type: 'CPU' | 'GPU';
  }[];
}

interface GetZoneListResult {
  zones: Zone[];
}

export function getZoneList(): BaseResponseType<GetZoneListResult> {
  return request({
    url: `${urlPrefix}/inventory`,
    method: 'GET'
  });
}

interface Subnet {
  subnetId: string;
  shortId: string;
  name: string;
  az: string;
}
interface GetSubnetListResult {
  page: {
    result: Subnet[];
  };
}

export function getSubnetList({vpcId}: {vpcId: string}): Promise<GetSubnetListResult> {
  return request({
    url: `${vpcUrlPrefix}/subnet/pageList`,
    method: 'POST',
    data: {
      vpcId
    }
  });
}
