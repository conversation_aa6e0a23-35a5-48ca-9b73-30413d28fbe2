/**
 * 元数据管理 - 接口集合
 */

import {request, BaseResponseType} from '@baidu/bce-react-toolkit';
import {handleDownLoad} from '@utils/handleDownLoad';
import qs from 'qs';
import {AxiosProgressEvent} from 'axios';

type ApiResPromise<T> = BaseResponseType<T>;

const baseUrl = '/api/databuilder/v1';

export interface ISearchMetastoreParams {
  /**
   * 名称过滤条件
   */
  filter?: null | string;
  /**
   * 当前工作空间 id
   */
  workspaceId: string;
}

export interface ISearchMetastoreRes {
  name: string;
  schemas: Array<{
    name: string;
    operators: Array<{name: string; [property: string]: any}>;
    tables: Array<{name: string; [property: string]: any}>;
    volumes: Array<{name: string; [property: string]: any}>;
    [property: string]: any;
  }>;
}

// 元数据 Tree 实体搜索
// ? workspaceId有问题
export function searchMetastore(params: ISearchMetastoreParams): ApiResPromise<ISearchMetastoreRes[]> {
  return request({
    url: `${baseUrl}/metastore/search`,
    method: 'POST',
    data: params
  });
}

/*****************  Catalog 相关接口  *****************/

// 获取 Catalog 列表
// ? workspaceId有问题
export function getCatalogList(params: {workspaceId: string}): ApiResPromise<{catalogs: string[]}> {
  return request({
    url: `${baseUrl}/catalogs`,
    method: 'GET',
    params
  });
}

export interface ICatalogParams {
  /**
   * 描述
   */
  comment?: null | string;
  /**
   * workspaceId 工作空间命名
   */
  workspaceId: string;
  /**
   * catalog 名称
   */
  name?: string;
}

// 创建 Catalog
// * 通了
export function createCatalog(params: ICatalogParams): ApiResPromise<any> {
  return request({
    url: `${baseUrl}/catalogs`,
    method: 'POST',
    data: params
  });
}

// 删除 Catalog
// * 通了 功能正常
export function deleteCatalog(fullName: string): ApiResPromise<any> {
  return request({
    url: `${baseUrl}/catalogs/${fullName}`,
    method: 'DELETE'
  });
}

// 修改 Catalog
// * 通了 功能正常
export function patchCatalog(
  fullName: string,
  params: Omit<ICatalogParams, 'workspaceId'>
): ApiResPromise<any> {
  return request({
    url: `${baseUrl}/catalogs/${fullName}`,
    method: 'PATCH',
    data: params
  });
}

// 数据目录 类型
export enum CatalogType {
  // 特殊的 Catalog（内置）
  SYSTEM = 'system',
  // 普通元数据
  DATALAKE = 'Datalake',
  // 查询检索实例
  DORIS = 'Doris'
}

/**
 * catalog 属性
 */
export interface CatalogProperties {
  /**
   * 查询检索实例ID。Doris 类型拥有
   */
  computeId?: string;
  /**
   * 查询检索实例名称。Doris 类型拥有
   */
  computeName?: string;
  [property: string]: string;
}

export interface ICatalogDetailRes {
  /**
   * 描述
   */
  comment?: null | string;
  /**
   * 创建时间
   */
  createdAt: Date;
  /**
   * 创建人
   */
  createdBy: string;
  /**
   * catalog 唯一标识
   */
  id: string;
  /**
   * catalog 名
   */
  name: string;
  /**
   * 拥有者
   */
  owner: string;
  /**
   * 属性
   */
  properties: CatalogProperties;
  /**
   * 存储路径
   */
  storageLocation: string;
  /**
   * 修改时间
   */
  updatedAt: Date;
  /**
   * 最近修改人
   */
  updatedBy: string;
  /**
   *  catalog 类型
   */
  type: CatalogType;
}

export interface ICatalogSummaryRes {
  /**
   * operator 数
   */
  operatorCount: number;
  /**
   * table 数
   */
  tableCount: number;
  /**
   * volume 数
   */
  volumeCount: number;
}

// Catalog 详情
// * 通了
export function getCatalogDetail(
  fullName: string,
  silent = false
): ApiResPromise<{
  catalog: ICatalogDetailRes;
  metadataSummary: ICatalogSummaryRes;
}> {
  return request({
    url: `${baseUrl}/catalogs/${fullName}/detail`,
    method: 'GET',
    silent
  });
}

export interface ICatalogWorkspace {
  /** 工作空间 ID */
  workspaceId: string;
  /** 工作空间名称 */
  workspaceName: string;
}

export interface IQueryCatalogWorkspaceListParams {
  /** 当前页码 */
  pageNo?: number;
  /** 每页条数 */
  pageSize?: number;
  /** 搜索条件：工作空间名称 */
  workspaceName?: string;
}

// 获取 Catalog下的工作空间列表
// * 通了
export function getCatalogWorkspaceList(
  catalogId: string,
  params: IQueryCatalogWorkspaceListParams
): ApiResPromise<{
  items: Array<ICatalogWorkspace>;
  total: number;
}> {
  return request({
    url: `${baseUrl}/catalogs/${catalogId}/assigns`,
    method: 'GET',
    params
  });
}

// 解绑 Catalog下的工作空间
// ! 后端没测完
export function deleteCatalogWorkspace(catalogId: string, params: {items: string[]}): ApiResPromise<any> {
  return request({
    url: `${baseUrl}/catalogs/${catalogId}/assign`,
    method: 'DELETE',
    data: params
  });
}

// 绑定 Catalog下的工作空间
// ! 后端没测完
export function assignCatalogWorkspace(
  catalogId: string,
  params: {items: ICatalogWorkspace[]}
): ApiResPromise<any> {
  return request({
    url: `${baseUrl}/catalogs/${catalogId}/assign`,
    method: 'POST',
    data: params
  });
}
/*****************  Catalog 结束  *****************/

/*****************  Scheme 相关接口  *****************/

export interface ISchemaListParams {
  /**
   * catalog 名称
   */
  catalogName: string;
  /**
   * metastore id
   */
  workspaceId: string;
}

// 获取 Scheme 列表
// ? workspaceId有问题
export function getSchemaList(params: ISchemaListParams): ApiResPromise<{schemas: string[]}> {
  return request({
    url: `${baseUrl}/schemas`,
    method: 'GET',
    params: params
  });
}

export interface ISchemaParams {
  /**
   * catalog 名
   */
  catalogName: string;
  /**
   * 描述
   */
  comment?: null | string;
  /**
   * schema 名
   */
  name: string;
}

// 创建 Schema
// * 通了
export function createSchema(params: ISchemaParams): ApiResPromise<any> {
  return request({
    url: `${baseUrl}/schemas`,
    method: 'POST',
    data: params
  });
}

// 删除 Schema
// * 通了
export function deleteSchema(fullName: string): ApiResPromise<any> {
  return request({
    url: `${baseUrl}/schemas/${fullName}`,
    method: 'DELETE'
  });
}

// 修改 Schema
// * 通了
export function patchSchema(
  fullName: string,
  params: Pick<ISchemaParams, 'name'> | Pick<ISchemaParams, 'comment'>
): ApiResPromise<any> {
  return request({
    url: `${baseUrl}/schemas/${fullName}`,
    method: 'PATCH',
    data: params
  });
}

export interface ISchemaSummaryRes {
  /**
   * operator 数
   */
  operatorCount: number;
  /**
   * table 数
   */
  tableCount: number;
  /**
   * volume 数
   */
  volumeCount: number;
}

export interface ISchemaDetailRes {
  /**
   * catalog 名
   */
  catalogName: string;
  /**
   * 描述
   */
  comment?: null | string;
  /**
   * 创建时间
   */
  createdAt: Date;
  /**
   * 创建人
   */
  createdBy: string;
  /**
   * schema 唯一标识
   */
  id: string;
  /**
   * schema 名
   */
  name: string;
  /**
   * 拥有者
   */
  owner: string;
  /**
   * 属性
   */
  properties: {[key: string]: string};
  /**
   * 存储路径
   */
  storageLocation: string;
  /**
   * 修改时间
   */
  updatedAt: Date;
  /**
   * 最近修改人
   */
  updatedBy: string;
}

// Schema 详情
// * 通了
export function getSchemaDetail(
  fullName: string,
  silent = false
): ApiResPromise<{
  metadataSummary: ISchemaSummaryRes;
  schema: ISchemaDetailRes;
}> {
  return request({
    url: `${baseUrl}/schemas/${fullName}/detail`,
    method: 'GET',
    silent
  });
}

/*****************  Scheme 结束  *****************/

/*****************  Table 相关接口  *****************/

export interface ITableListParams {
  /**
   * catalog 名称
   */
  catalogName: string;
  /**
   * metastore id
   */
  workspaceId: string;
  /**
   * schema 名称
   */
  schemaName: string;
}

// 获取 Table 列表
// ! 后端没测完
export function getTableList(params: ITableListParams): ApiResPromise<{tables: string[]}> {
  return request({
    url: `${baseUrl}/tables`,
    method: 'GET',
    params: params
  });
}

export interface ITableColumn {
  /**
   * 列的描述
   */
  comment?: string;
  /**
   * 列名
   */
  name: string;
  /**
   * 是否非空
   */
  nullable: boolean;
  /**
   * 分区列的序数位置。从 0 开始
   */
  partitionIndex: number;
  /**
   * 列的序数位置，从 0 开始
   */
  position: number;
  /**
   * IntervalType 的格式
   */
  typeIntervalType?: null | string;
  /**
   * JSON 格式的数据类型描述
   */
  typeJson?: null | string;
  /**
   * 列类型
   */
  typeName: ITableTypeName;
  /**
   * DecimalType 的精度
   */
  typePrecision?: number | null;
  /**
   * DecimalType 的标度，即小数部分的位数
   */
  typeScale?: number | null;
  /**
   * SQL形式的数据类型描述
   */
  typeText?: null | string;
}
export interface ITableParams {
  /**
   * 存储访问 ak。EXTERNAL 必须
   */
  accessKeyId?: string;
  /**
   * catalog 名
   */
  catalogName: string;
  /**
   * 列
   */
  columns: ITableColumn[];
  /**
   * 描述
   */
  comment?: null | string;
  /**
   * 数据源格式
   */
  dataSourceFormat: ITableDataSourceFormat;
  /**
   * table 名
   */
  name: string;
  properties?: {[key: string]: any};
  /**
   * schema 名
   */
  schemaName: string;
  /**
   * 存储访问 sk。EXTERNAL 必须
   */
  secretAccessKey?: string;
  /**
   * EXTERNAL 必须
   */
  storageLocation?: string;
  /**
   * 表类型
   */
  tableType: 'EXTERNAL' | 'MANAGED';
}

export enum ITableTypeName {
  Array = 'ARRAY',
  Binary = 'BINARY',
  Boolean = 'BOOLEAN',
  Byte = 'BYTE',
  Char = 'CHAR',
  Date = 'DATE',
  Decimal = 'DECIMAL',
  Double = 'DOUBLE',
  Float = 'FLOAT',
  Int = 'INT',
  Interval = 'INTERVAL',
  Long = 'LONG',
  Map = 'MAP',
  Null = 'NULL',
  Short = 'SHORT',
  String = 'STRING',
  Struct = 'STRUCT',
  TableType = 'TABLE_TYPE',
  Timestamp = 'TIMESTAMP',
  TimestampNtz = 'TIMESTAMP_NTZ',
  UserDefineType = 'USER_DEFINE_TYPE'
}

export enum ITableDataSourceFormat { // table 数据源格式
  Avro = 'AVRO',
  Csv = 'CSV',
  Iceberg = 'ICEBERG',
  Json = 'JSON',
  Orc = 'ORC',
  Parquet = 'PARQUET',
  Text = 'TEXT'
}

// 创建 Table
// 本期不涉及
export function createTable(params: any): ApiResPromise<any> {
  return request({
    url: `${baseUrl}/tables`,
    method: 'POST',
    data: params
  });
}

// 删除 Table
// * 通
export function deleteTable(fullName: string): ApiResPromise<any> {
  return request({
    url: `${baseUrl}/tables/${fullName}`,
    method: 'DELETE'
  });
}

// 修改 Table
// *
export function patchTable(
  fullName: string,
  params: Pick<ITableParams, 'name'> | Pick<ITableParams, 'comment'>
): ApiResPromise<any> {
  return request({
    url: `${baseUrl}/tables/${fullName}`,
    method: 'PATCH',
    data: params
  });
}

export interface ITableDetailRes {
  /**
   * catalog 名
   */
  catalogName: string;
  /**
   * 列
   */
  columns: ITableColumn[];
  /**
   * 描述
   */
  comment?: null | string;
  /**
   * 创建时间
   */
  createdAt: Date;
  /**
   * 创建人
   */
  createdBy: string;
  /**
   * 数据源格式
   */
  dataSourceFormat: ITableDataSourceFormat;
  /**
   * table 唯一标识
   */
  id: string;
  /**
   * table 名
   */
  name: string;
  /**
   * 拥有者
   */
  owner: string;
  /**
   * 属性
   */
  properties: {[key: string]: string};
  /**
   * schema 名
   */
  schemaName: string;
  /**
   * 存储路径
   */
  storageLocation: string;
  /**
   * 表类型
   */
  tableType: 'EXTERNAL' | 'MANAGED';
  /**
   * 修改时间
   */
  updatedAt: Date;
  /**
   * 最近修改人
   */
  updatedBy: string;
}

// Table 详情
// * 通了
export function getTableDetail(fullName: string, silent = false): ApiResPromise<ITableDetailRes> {
  return request({
    url: `${baseUrl}/tables/${fullName}`,
    method: 'GET',
    silent
  });
}

/*****************  Table 结束  *****************/

/*****************  Volume 相关接口  *****************/

export interface IVolumeListParams {
  /**
   * catalog 名称
   */
  catalogName: string;
  /**
   * metastore id
   */
  workspaceId: string;
  /**
   * schema 名称
   */
  schemaName: string;
}

// 获取 Volume 列表
// ? workspaceId影响
export function getVolumeList(params: IVolumeListParams): ApiResPromise<{volumes: string[]}> {
  return request({
    url: `${baseUrl}/volumes`,
    method: 'GET',
    params: params
  });
}

// volume 类型
export enum EVolumeType {
  EXTERNAL = 'EXTERNAL',
  MANAGED = 'MANAGED'
}

export interface IVolumeParams {
  /**
   * 存储访问 ak。EXTERNAL 必须
   */
  accessKeyId?: string;
  /**
   * catalog 名
   */
  catalogName: string;
  /**
   * 描述
   */
  comment?: null | string;
  /**
   * volume 名
   */
  name: string;
  properties?: {[key: string]: any};
  /**
   * schema 名
   */
  schemaName: string;
  /**
   * 存储访问 sk。EXTERNAL 必须
   */
  secretAccessKey?: string;
  /**
   * 存储路径。EXTERNAL 必须
   */
  storageLocation?: string;
  /**
   * volume 类型
   */
  volumeType: EVolumeType;
}

// 创建 Volume
// * 通了
export function createVolume(params: IVolumeParams): ApiResPromise<any> {
  return request({
    url: `${baseUrl}/volumes`,
    method: 'POST',
    data: params
  });
}

// 删除 Volume
// * 通了
export function deleteVolume(fullName: string): ApiResPromise<any> {
  return request({
    url: `${baseUrl}/volumes/${fullName}`,
    method: 'DELETE'
  });
}

// 修改 Volume
// * 通了
export function patchVolume(
  fullName: string,
  params: Pick<IVolumeParams, 'comment'> | Pick<IVolumeParams, 'name'>
): ApiResPromise<any> {
  return request({
    url: `${baseUrl}/volumes/${fullName}`,
    method: 'PATCH',
    data: params
  });
}

export interface IVolumeDetailRes {
  /**
   * catalog 名
   */
  catalogName: string;
  /**
   * 描述
   */
  comment?: null | string;
  /**
   * 创建时间
   */
  createdAt: Date;
  /**
   * 创建人
   */
  createdBy: string;
  /**
   * volume 唯一标识
   */
  id: string;
  /**
   * volume 名
   */
  name: string;
  /**
   * 拥有者
   */
  owner: string;
  /**
   * 属性
   */
  properties: {[key: string]: string};
  /**
   * schema 名
   */
  schemaName: string;
  /**
   * 存储路径
   */
  storageLocation: string;
  /**
   * 修改时间
   */
  updatedAt: Date;
  /**
   * 最近修改人
   */
  updatedBy: string;
  /**
   * volume 类型
   */
  volumeType: 'EXTERNAL' | 'MANAGED';
}

// Volume 详情
// * 通了
export function getVolumeDetail(fullName: string, silent = false): ApiResPromise<IVolumeDetailRes> {
  return request({
    url: `${baseUrl}/volumes/${fullName}`,
    method: 'GET',
    silent
  });
}

/*****************  Volume 结束  *****************/
/*****************  Volume 文件管理接口  *****************/

export interface IVolumeFileListQuerys {
  /**
   * 当前页从 marker 指向的下一个文件开始。默认为""
   */
  marker?: string;
  /**
   * 页大小。默认30，最大200。
   */
  maxSize?: number;
  /**
   * volume虚拟路径。形如 {catalogName}/{schemaName}/{volumeName}/path/to/your/file
   */
  path: string;
}

export interface IVolumeListRes {
  /**
   * 文件或文件夹列表
   */
  files: IVolumeListFile[];
  /**
   * 查询本页marker的值
   */
  marker: string;
  /**
   * 查询下页时marker的值
   */
  nextMarker: string;
  /**
   * 当前目录虚拟路径
   */
  path: string;
  /**
   * 本次是否已返回全部结果
   */
  truncated: boolean;
}

export interface IVolumeListFile {
  /**
   * 文件或文件夹名。以"/"结尾的为文件夹
   */
  name: string;
  /**
   * 当前文件虚拟路径
   */
  path: string;
  /**
   * 文件字节大小
   */
  size: number;
  /**
   * 最近修改时间
   */
  updatedAt: Date;
}

// 获取 Volume 文件列表
// ? 修改volume会不可访问 后端bug
export function getVolumeFileList(
  fullName: string,
  params: IVolumeFileListQuerys
): ApiResPromise<IVolumeListRes> {
  return request({
    url: `${baseUrl}/volumes/${fullName}/files`,
    method: 'GET',
    params
  });
}

export interface IUploadVolumeFileParams extends FormData {
  file: any; // application/form-data 的文件
}

// Volume 文件上传
// * 通了
export function uploadVolumeFile(
  fullName: string,
  data: IUploadVolumeFileParams,
  path: string,
  onUploadProgress?: (progressEvent: AxiosProgressEvent) => void,
  controller?: AbortController
): ApiResPromise<any> {
  return request({
    url: `${baseUrl}/volumes/${fullName}/files`,
    method: 'POST',
    data: data,
    params: {path},
    onUploadProgress,
    signal: controller.signal
  });
}

// Volume 文件删除
// * 通
export function deleteVolumeFile(fullName: string, params: {files: string[]}): ApiResPromise<any> {
  return request({
    url: `${baseUrl}/volumes/${fullName}/files`,
    method: 'DELETE',
    data: params,
    paramsSerializer: (params) => qs.stringify(params, {arrayFormat: 'comma'})
  });
}

// Volume 文件下载
// * 通
export function downloadVolumeFile(fullName: string, params: {file: string}): void {
  request({
    url: `${baseUrl}/volumes/${fullName}/files/download`,
    method: 'POST',
    data: params,
    raw: true,
    responseType: 'blob'
  }).then((res) => handleDownLoad(res));
}

/*****************  Volume 文件管理结束  *****************/

/*****************  Operator  相关接口  *****************/

export interface IOperatorListQuerys {
  /**
   * catalog 名称
   */
  catalogName: string;
  /**
   * metastore id
   */
  workspaceId: string;
  /**
   * schema 名称
   */
  schemaName: string;
}

export interface IOperatorParams {
  /**
   * 别名，显示名称
   */
  alias?: string;
  /**
   * catalog 名
   */
  catalogName: string;
  /**
   * 描述
   */
  comment?: null | string;
  /**
   * operator 名
   */
  name: string;
  properties?: {[key: string]: string};
  /**
   * schema 名
   */
  schemaName: string;
  /**
   * 使用说明
   */
  usage?: null | string;
}

// 获取 Operator 列表
// * 通
export function getOperatorList(params: IOperatorListQuerys): ApiResPromise<{operators: string[]}> {
  return request({
    url: `${baseUrl}/operators`,
    method: 'GET',
    params
  });
}

// 创建 Operator
// 本期没有
export function createOperator(params: IOperatorParams): ApiResPromise<any> {
  return request({
    url: `${baseUrl}/operators`,
    method: 'POST',
    data: params
  });
}

// 删除 Operator
// 本期没有
export function deleteOperator(fullName: string): ApiResPromise<any> {
  return request({
    url: `${baseUrl}/operators/${fullName}`,
    method: 'DELETE'
  });
}

export interface IOperatorPathcParams {
  /**
   * 新别名
   */
  alias?: string;
  /**
   * 描述
   */
  comment?: null | string;
  /**
   * 新 operator 名
   */
  name?: string;
  /**
   * 使用说明
   */
  usage?: null | string;
}

// 修改 Operator
// 本期没有
export function patchOperator(fullName: string, params: IOperatorPathcParams): ApiResPromise<any> {
  return request({
    url: `${baseUrl}/operators/${fullName}`,
    method: 'PATCH',
    data: params
  });
}

export interface IOperatorDetailRes {
  /**
   * operator 别名，显示名称
   */
  alias: string;
  /**
   * catalog 名
   */
  catalogName: string;
  /**
   * 描述
   */
  comment?: null | string;
  /**
   * 创建时间
   */
  createdAt: Date;
  /**
   * 创建人
   */
  createdBy: string;
  /**
   * operator 唯一标识
   */
  id: string;
  /**
   * 最新版本 ID
   */
  latestVersionId: string;
  /**
   * 最新版本号
   */
  latestVersionName: string;
  /**
   * operator 名
   */
  name: string;
  /**
   * 拥有者
   */
  owner: string;
  /**
   * 属性
   */
  properties: {[key: string]: string};
  /**
   * schema 名
   */
  schemaName: string;
  /**
   * 修改时间
   */
  updatedAt: Date;
  /**
   * 最近修改人
   */
  updatedBy: string;
  /**
   * 使用说明
   */
  usage?: null | string;
}

// 查询 Operator 详情
// * 通
export function getOperatorDetail(fullName: string, silent?: boolean): ApiResPromise<IOperatorDetailRes> {
  return request({
    url: `${baseUrl}/operators/${fullName}`,
    method: 'GET',
    silent
  });
}

/**
 * 算子类型
 */
export enum IOperCategory {
  Dedup = 'DEDUP',
  Embedding = 'EMBEDDING',
  Extract = 'EXTRACT',
  Filter = 'FILTER',
  Others = 'OTHERS',
  Transform = 'TRANSFORM'
}

export interface IOperParameterSchema {
  /**
   * 字段描述
   */
  comment?: string;
  /**
   * 默认值
   */
  defaultValue?: string;
  /**
   * 字段名
   */
  name: string;
  /**
   * 是否必须
   */
  required?: boolean;
  /**
   * 字段类型
   */
  type: string;
}

export interface IOperInputOutput {
  /**
   * 字段描述
   */
  comment?: string;
  /**
   * 字段名
   */
  name: string;
  /**
   * 是否必须
   */
  required?: boolean;
  /**
   * 字段类型
   */
  type: string;
  /**
   * 默认值
   */
  defaultValue?: string;
}

export interface IOperVersionOneRes {
  /**
   * 算子类型
   */
  category: IOperCategory;
  /**
   * catalog 名
   */
  catalogName: string;
  /**
   * 描述
   */
  comment?: null | string;
  /**
   * 创建时间
   */
  createdAt: Date;
  /**
   * 创建人
   */
  createdBy: string;
  /**
   * 引擎类型
   */
  engineType: Array<'RAY' | 'SPARK'>;
  /**
   * 运行参数
   */
  execParams: IOperParameterSchema[];
  /**
   * version 唯一标识
   */
  id: string;
  /**
   * 输入参数
   */
  input: IOperInputOutput[];
  /**
   * 代码语言
   */
  language: string;
  /**
   * version 名
   */
  name: string;
  /**
   * operator 名
   */
  operatorName: string;
  /**
   * 输出参数
   */
  output: IOperInputOutput[];
  /**
   * 拥有者
   */
  owner: string;
  /**
   * 属性
   */
  properties: {[key: string]: string};
  /**
   * 资源类型
   */
  resourceType: 'CPU' | 'GPU';
  /**
   * 运行环境
   */
  runtimeEnv: string;
  /**
   * schema 名
   */
  schemaName: string;
  /**
   * 算子代码路径
   */
  storageLocation: string;
  /**
   * 修改时间
   */
  updatedAt: Date;
  /**
   * 最近修改人
   */
  updatedBy: string;
}

// 获取 Operator 某一个算子版本详情
// ? 暂时不需要
export function getOperatorOneDetail(
  fullName: string,
  versionName: string
): ApiResPromise<IOperVersionOneRes> {
  return request({
    url: `${baseUrl}/operators/${fullName}/versions/${versionName}`,
    method: 'GET'
  });
}

export interface IOperAllVersionRes {
  /**
   * 版本总数
   */
  totals: number;
  /**
   * Operator 版本列表
   */
  versions: IOperVersionOneRes[];
}

// 获取 Operator 算子版本列表
// * 通
export function getOperatorAllList(
  fullName: string,
  params: {
    /**
     * 页号。从 1 开始，默认值为1
     */
    pageNo?: number;
    /**
     * 页大小。默认10，最大100。
     */
    pageSize?: number;
  }
): ApiResPromise<IOperAllVersionRes> {
  return request({
    url: `${baseUrl}/operators/${fullName}/versions`,
    method: 'GET',
    params
  });
}

// 新增 Operator 算子版本  (一期不支持)

// 删除 Operator 算子版本 (一期不支持)

// 修改 Operator 算子版本 (一期不支持)

/*****************  Operator  结束  *****************/
