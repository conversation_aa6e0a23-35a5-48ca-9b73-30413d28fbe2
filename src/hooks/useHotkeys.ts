import {useEffect, useRef, useCallback} from 'react';

// 快捷键配置类型
export interface HotkeyConfig {
  key: string; // 按键，如 's'
  metaKey?: boolean; // Mac 的 Command 键
  ctrlKey?: boolean; // Ctrl 键
  altKey?: boolean; // Alt 键
  shiftKey?: boolean; // Shift 键
  description: string; // 快捷键描述
  handler: (e: KeyboardEvent) => void; // 处理函数
  preventDefault?: boolean; // 是否阻止默认行为，默认为 true
  stopPropagation?: boolean; // 是否阻止事件冒泡，默认为 false
  disabled?: boolean; // 是否禁用快捷键
}

// 快捷键上下文类型
export type HotkeyContext = string;

// 全局快捷键存储
const globalHotkeys: Map<HotkeyContext, HotkeyConfig[]> = new Map();

// 当前激活的上下文
let activeContexts: Set<HotkeyContext> = new Set(['global']);

/**
 * 设置当前激活的上下文
 * @param contexts 上下文列表
 */
export const setActiveHotkeyContexts = (contexts: HotkeyContext[]) => {
  activeContexts = new Set(['global', ...contexts]);
};

/**
 * 添加上下文到当前激活的上下文列表
 * @param context 上下文
 */
export const addActiveHotkeyContext = (context: HotkeyContext) => {
  activeContexts.add(context);
};

/**
 * 从当前激活的上下文列表中移除上下文
 * @param context 上下文
 */
export const removeActiveHotkeyContext = (context: HotkeyContext) => {
  if (context !== 'global') {
    activeContexts.delete(context);
  }
};

/**
 * 获取当前激活的上下文列表
 */
export const getActiveHotkeyContexts = (): HotkeyContext[] => {
  return Array.from(activeContexts);
};

/**
 * 检查快捷键是否匹配事件
 * @param hotkey 快捷键配置
 * @param e 键盘事件
 */
const isHotkeyMatch = (hotkey: HotkeyConfig, e: KeyboardEvent): boolean => {
  // 检查是否禁用
  if (hotkey.disabled) {
    return false;
  }

  // 检查基本按键是否匹配
  if (e.key.toLowerCase() !== hotkey.key.toLowerCase()) {
    return false;
  }

  // 检查修饰键是否匹配
  if (
    (hotkey.metaKey !== undefined && e.metaKey !== hotkey.metaKey) ||
    (hotkey.ctrlKey !== undefined && e.ctrlKey !== hotkey.ctrlKey) ||
    (hotkey.altKey !== undefined && e.altKey !== hotkey.altKey) ||
    (hotkey.shiftKey !== undefined && e.shiftKey !== hotkey.shiftKey)
  ) {
    return false;
  }

  return true;
};

/**
 * 全局键盘事件处理函数
 * @param e 键盘事件
 */
const handleGlobalKeyDown = (e: KeyboardEvent) => {
  // 遍历所有激活的上下文
  for (const context of activeContexts) {
    const hotkeys = globalHotkeys.get(context);
    if (!hotkeys) continue;

    // 遍历上下文中的所有快捷键
    for (const hotkey of hotkeys) {
      if (isHotkeyMatch(hotkey, e)) {
        // 阻止默认行为
        if (hotkey.preventDefault !== false) {
          e.preventDefault();
        }

        // 阻止事件冒泡
        if (hotkey.stopPropagation) {
          e.stopPropagation();
        }

        // 执行处理函数
        hotkey.handler(e);
        return;
      }
    }
  }
};

// 初始化全局事件监听
let isInitialized = false;
const initGlobalListener = () => {
  if (isInitialized) return;

  document.addEventListener('keydown', handleGlobalKeyDown);
  isInitialized = true;
};

/**
 * 注册快捷键到指定上下文
 * @param context 上下文
 * @param hotkey 快捷键配置
 */
export const registerHotkey = (context: HotkeyContext, hotkey: HotkeyConfig) => {
  if (!globalHotkeys.has(context)) {
    globalHotkeys.set(context, []);
  }

  const contextHotkeys = globalHotkeys.get(context)!;

  // 检查是否已存在相同的快捷键
  const existingIndex = contextHotkeys.findIndex(
    (h) =>
      h.key === hotkey.key &&
      h.metaKey === hotkey.metaKey &&
      h.ctrlKey === hotkey.ctrlKey &&
      h.altKey === hotkey.altKey &&
      h.shiftKey === hotkey.shiftKey
  );

  if (existingIndex >= 0) {
    // 替换已存在的快捷键
    contextHotkeys[existingIndex] = hotkey;
  } else {
    // 添加新的快捷键
    contextHotkeys.push(hotkey);
  }

  // 确保全局监听器已初始化
  initGlobalListener();
};

/**
 * 取消注册指定上下文中的快捷键
 * @param context 上下文
 * @param key 按键
 * @param metaKey 是否需要 Meta 键
 * @param ctrlKey 是否需要 Ctrl 键
 * @param altKey 是否需要 Alt 键
 * @param shiftKey 是否需要 Shift 键
 */
export const unregisterHotkey = (
  context: HotkeyContext,
  key: string,
  metaKey?: boolean,
  ctrlKey?: boolean,
  altKey?: boolean,
  shiftKey?: boolean
) => {
  if (!globalHotkeys.has(context)) return;

  const contextHotkeys = globalHotkeys.get(context)!;

  const newHotkeys = contextHotkeys.filter(
    (h) =>
      h.key !== key ||
      h.metaKey !== metaKey ||
      h.ctrlKey !== ctrlKey ||
      h.altKey !== altKey ||
      h.shiftKey !== shiftKey
  );

  globalHotkeys.set(context, newHotkeys);
};

/**
 * 获取指定上下文中的所有快捷键
 * @param context 上下文
 */
export const getHotkeys = (context: HotkeyContext): HotkeyConfig[] => {
  return globalHotkeys.get(context) || [];
};

/**
 * 获取所有上下文中的快捷键
 */
export const getAllHotkeys = (): Record<HotkeyContext, HotkeyConfig[]> => {
  const result: Record<HotkeyContext, HotkeyConfig[]> = {};

  globalHotkeys.forEach((hotkeys, context) => {
    result[context] = [...hotkeys];
  });

  return result;
};

/**
 * 使用快捷键的 Hook
 * @param context 上下文
 * @param hotkeys 快捷键配置列表
 */
export const useHotkeys = (context: HotkeyContext, hotkeys: HotkeyConfig[]) => {
  // 使用 ref 存储最新的 hotkeys，避免 useEffect 依赖变化导致频繁重新注册
  const hotkeysRef = useRef<HotkeyConfig[]>(hotkeys);

  // 更新 ref 中的值
  useEffect(() => {
    hotkeysRef.current = hotkeys;
  }, [hotkeys]);

  // 注册快捷键
  useEffect(() => {
    // 添加上下文到激活列表
    addActiveHotkeyContext(context);

    // 注册所有快捷键
    hotkeys.forEach((hotkey) => {
      registerHotkey(context, hotkey);
    });

    // 清理函数
    return () => {
      // 从激活列表中移除上下文
      removeActiveHotkeyContext(context);

      // 取消注册所有快捷键
      hotkeys.forEach((hotkey) => {
        unregisterHotkey(context, hotkey.key, hotkey.metaKey, hotkey.ctrlKey, hotkey.altKey, hotkey.shiftKey);
      });
    };
  }, [context]);
};

/**
 * 创建快捷键配置的辅助函数
 * @param key 按键
 * @param handler 处理函数
 * @param options 其他选项
 */
export const createHotkey = (
  key: string,
  handler: (e: KeyboardEvent) => void,
  options: Partial<Omit<HotkeyConfig, 'key' | 'handler'>> = {}
): HotkeyConfig => {
  return {
    key,
    handler,
    description: options.description || key,
    metaKey: options.metaKey,
    ctrlKey: options.ctrlKey,
    altKey: options.altKey,
    shiftKey: options.shiftKey,
    preventDefault: options.preventDefault !== false,
    stopPropagation: options.stopPropagation || false,
    disabled: options.disabled || false
  };
};

export const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
/**
 * 创建通用的保存快捷键（同时支持 Mac 和 Windows）
 * @param handler 处理函数
 * @param description 描述
 */
export const createSaveHotkey = (
  handler: (e: KeyboardEvent) => void,
  description: string = '保存'
): HotkeyConfig => {
  // 检测操作系统
  const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;

  return createHotkey('s', handler, {
    description: `${description} (${isMac ? '⌘+S' : 'Ctrl+S'})`,
    metaKey: isMac, // Mac 使用 Command 键
    ctrlKey: !isMac // 非 Mac 使用 Ctrl 键
  });
};

// 导出一些常用的快捷键创建函数
export const HotkeyCreators = {
  save: createSaveHotkey
  // 可以添加更多常用快捷键
};
