// 导入useDispatch 用于派发 action；
// 导入 useSelector 用于从 Redux store 中获取状态
import {useDispatch, useSelector} from 'react-redux';
// 导入对应的 store 类型声明
import {IAppDispatch, IAppState} from '@store/index';
// 导入对应的 action
import {decremented, incremented} from '@store/sumSlice';

import {Button} from 'acud';

const DemoRedux = () => {
  // 使用 useSelector 获取当前的状态值
  const counterValue = useSelector((state: IAppState) => state.sumSlice.value);

  // 使用 useDispatch 获取 dispatch 函数
  const dispatch = useDispatch<IAppDispatch>();

  return (
    <>
      <h1>这是一个demo: 整体应用 @reduxjs/toolkit 的方式</h1>
      <ol>
        <li>
          1、使用 createSlice 创建一个 reducer, 不同业务模块以独立文件来创建
        </li>
        <li>2、使用 configureStore 创建一个 store</li>
        <li>3、使用 useSelector 获取当前的状态值</li>
        <li>4、使用 useDispatch 获取 dispatch 函数</li>
      </ol>
      <br />
      <h2>示例：加减法</h2>
      <div>当前值: {counterValue}</div>
      <Button onClick={() => dispatch(incremented())}>加一</Button>
      <Button onClick={() => dispatch(decremented())}>减一</Button>
      <h1 className="text-3xl font-bold underline">tailwind 测试</h1>
    </>
  );
};

export default DemoRedux;
