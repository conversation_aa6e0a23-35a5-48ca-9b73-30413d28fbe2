/**
 * 重命名弹窗： Catalog、Schema、Table、Volume、Operator
 * <AUTHOR>
 */

import React, {useState} from 'react';
import {Form, Input, Modal, toast} from 'acud';
import {RuleObject} from 'acud/lib/form';
import {patchCatalog, patchSchema, patchTable, patchVolume, patchOperator} from '@api/metaRequest';
import {formItemLayout} from '../helper';
import styles from './ModalStyle.module.less';

type IrequestFun =
  | typeof patchCatalog
  | typeof patchSchema
  | typeof patchTable
  | typeof patchVolume
  | typeof patchOperator;

interface IModalCreateNameProps {
  visible: boolean; // 弹窗可见
  setVisible: React.Dispatch<React.SetStateAction<boolean>>; // 更改弹窗可见
  title: string; // 弹窗标题 & 表单名称前缀
  fullName: string; // catalog 全名。{metastoreId}.{catalogName}
  nowValue: string; // 当前值
  nameRules?: RuleObject[]; // 表单名称校验规则
  limitLength?: number; // 输入框最大长度
  requestFun: IrequestFun; // 点击确定 patch 更新的请求方法
  requestParams?: {[key: string]: any}; // 额外的请求参数
  successFun: (...args: any[]) => any; // requestFun 请求成功后回调
  forbidIfLimit?: boolean; // 超出长度是否禁用
}

const ModalRename = (props: IModalCreateNameProps) => {
  const {
    visible,
    setVisible,
    title,
    nowValue,
    requestFun,
    requestParams,
    nameRules = [],
    successFun,
    fullName,
    limitLength = 64,
    forbidIfLimit = true
  } = props;

  const [form] = Form.useForm();

  // 确定按钮 loading
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);

  const handleCancel = () => {
    setVisible(false);
    setConfirmLoading(false);
    setTimeout(() => form.resetFields(), 100); // 延迟清空，优化弹窗消失动效
  };

  const handleOk = async () => {
    try {
      await form.validateFields();
    } catch {
      return;
    }
    // formData 值为： {name:xxx}
    const formData = form.getFieldsValue();
    setConfirmLoading(true);
    try {
      const res = await requestFun(fullName, {...requestParams, ...formData});
      if (res?.success != true) {
        setConfirmLoading(false);
        return;
      }
      toast.success({
        message: '更新成功！',
        duration: 5
      });
      successFun(formData);
      handleCancel();
    } catch {
      return;
    }
  };

  return (
    <Modal
      className={styles['modal-form-reset-acud']}
      title={`重命名 ${title}`}
      visible={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      confirmLoading={confirmLoading}
    >
      <Form {...formItemLayout} form={form} name="create-name-modal">
        <Form.Item label={`原${title}名称`}>
          <Input placeholder="请输入名称" disabled value={nowValue} />
        </Form.Item>
        <Form.Item
          name="name"
          label={`${title}名称`}
          rules={[
            {
              required: true,
              message: '名称不可为空'
            },
            ...nameRules
          ]}
          validateTrigger="onBlur"
        >
          <Input placeholder="请输入新名称" limitLength={limitLength} forbidIfLimit={forbidIfLimit} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ModalRename;
