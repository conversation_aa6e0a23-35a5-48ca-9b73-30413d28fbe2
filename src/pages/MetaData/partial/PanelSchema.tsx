import {useCallback, useEffect, useMemo, useState} from 'react';

import DescriptionEdit from '@components/DescriptionEdit';
import InfoPanel from '@components/InfoPanel';
import IconSvg from '@components/IconSvg';

import * as http from '@api/metaRequest';
import {EnumNodeType, IUrlStateHandler} from '../index';

import MetaActionBar from '../components/MetaActionBar';
import MetaTabs from '../components/MetaTabs';
import ModalRemoveFun from '../components/ModalRemoveFun';
import ModalRename from '../components/ModalRename';
import CreateVolumeModal from '../components/CreateVolumeModal';
import {OutlinedPlusNew} from 'acud-icon';
import {MetaCnNameMap, ruleMapByCatalog} from '../config';
import {CatalogType} from '@api/metaRequest';
import {Loading} from 'acud';

enum PanelEnum {
  DETAIL = '1'
}

const initialPanes = [{tab: '详情', key: PanelEnum.DETAIL}];

const PanelSchema = (props: IUrlStateHandler) => {
  const {urlState, changeUrlFun, changeUrlFunReplace, handleTreeRefresh, userList, canWrite} = props;
  const {catalog = '', schema = '', tab = PanelEnum.DETAIL} = urlState;

  // schema 全名
  const fullName = `${catalog}.${schema}`;

  const [isCreateVolumeModalVisible, setIsCreateVolumeModalVisible] = useState(false);

  // 详情
  const [dataInfo, setDataInfo] = useState<{
    schema?: http.ISchemaDetailRes;
    metadataSummary?: http.ISchemaSummaryRes;
  }>({});

  // catalog 类型
  const [catalogType, setCatalogType] = useState<CatalogType>(CatalogType.SYSTEM);

  const [loading, setLoading] = useState<boolean>(false);

  const dropdownMenu = useMemo(
    () => [
      {key: 'rename', label: `重命名${MetaCnNameMap['Schema']}`, disable: !canWrite},
      {key: 'remove', label: `删除${MetaCnNameMap['Schema']}`, disable: !canWrite}
    ],
    [canWrite]
  );

  // 立即新建按钮「下拉列」
  const createMenu = useMemo(() => {
    if (catalogType === CatalogType.DORIS) {
      return [];
    }
    return [
      // {key: 'addTable', label: '创建 Table'},
      {key: 'addVolume', label: `创建${MetaCnNameMap['Volume']}`, disable: !canWrite}
      // {key: 'addOperator', label: '创建 Operator'}
    ];
  }, [canWrite, catalogType]);

  // 详情字段
  const infoList = useMemo(() => {
    const info: any = dataInfo?.schema || {};
    const dorisInfo = [
      {
        label: `${MetaCnNameMap['Catalog']}名称`,
        value: info.catalogName
      },
      {
        label: `${MetaCnNameMap['Schema']}名称`,
        value: info.name
      },
      {
        label: `${MetaCnNameMap['Table']}个数`,
        value: `${dataInfo.metadataSummary?.tableCount || 0}个`
      }
    ];
    return catalogType === CatalogType.DORIS
      ? dorisInfo
      : [
          {
            label: `${MetaCnNameMap['Catalog']}名称`,
            value: info.catalogName
          },
          {
            label: `${MetaCnNameMap['Schema']}名称`,
            value: info.name
          },
          {
            label: '描述',
            value: info.comment
          },
          {
            label: '类型',
            value: catalogType
          },
          {
            label: '存储类型',
            value: info.storageType
          },
          {
            label: '存储格式',
            value: info.storageFormat
          },
          {
            label: '存储路径',
            value: info.storageLocation
          },
          {
            label: '创建时间',
            value: info.createdAt
          },
          {
            label: '创建人',
            value: userList.find((item) => item.id === info.createdBy)?.name || info.createdBy
          },
          {
            label: '修改时间',
            value: info.updatedAt
          },
          {
            label: '最近修改人',
            value: userList.find((item) => item.id === info.updatedBy)?.name || info.updatedBy
          },
          {
            label: `${MetaCnNameMap['Table']}个数`,
            value: `${dataInfo.metadataSummary?.tableCount || 0}个`
          },
          {
            label: `${MetaCnNameMap['Volume']}个数`,
            value: `${dataInfo.metadataSummary?.volumeCount || 0}个`
          },
          {
            label: `${MetaCnNameMap['Operator']}个数`,
            value: `${dataInfo.metadataSummary?.operatorCount || 0}个`
          }
        ];
  }, [
    catalogType,
    dataInfo.metadataSummary?.operatorCount,
    dataInfo.metadataSummary?.tableCount,
    dataInfo.metadataSummary?.volumeCount,
    dataInfo?.schema,
    userList
  ]);

  // 获取详情
  const getSchemaDetail = async () => {
    setLoading(true);
    try {
      // 获取 catalog 类型，根据类型获取详情展示形式，system 不需要请求
      if (catalog !== CatalogType.SYSTEM) {
        const catalogDetail = await http.getCatalogDetail(catalog);
        setCatalogType(catalogDetail?.result?.catalog?.type);
      }

      const res = await http.getSchemaDetail(fullName);
      setDataInfo(res.result);
    } catch {
      console.error('获取 Schema 详情失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化
  useEffect(() => {
    catalog && schema && getSchemaDetail();
  }, [catalog, schema]);

  // 更新描述
  const onChangeDescript = useCallback(
    async (text: string) => {
      await http.patchSchema(fullName, {comment: text});
      getSchemaDetail();
    },
    [dataInfo, setDataInfo, catalog, schema]
  );

  // 重命名 & 删除
  const [showRenameModal, setRenameModal] = useState<boolean>(false);
  const renameSuccessFun = useCallback(
    (formData: {name: string}) => {
      changeUrlFunReplace((preState) => ({...preState, schema: formData.name}), true);
    },
    [changeUrlFunReplace]
  );
  const removeSuccessFun = useCallback(() => {
    changeUrlFunReplace(
      (preState) => ({
        ...preState,
        catalog: CatalogType.SYSTEM,
        schema: '',
        type: ''
      }),
      true
    );
  }, [changeUrlFunReplace]);
  const onDropdownClick = useCallback(
    (key) => {
      if (key === 'rename') {
        setRenameModal(true);
      } else if (key === 'remove') {
        ModalRemoveFun({
          fullName,
          name: schema,
          title: 'Schema',
          requestFun: http.deleteSchema,
          successFun: removeSuccessFun
        });
      }
    },
    [schema, removeSuccessFun]
  );

  // 新建 Table & Volume & Operator
  const onCreateClick = useCallback((key) => {
    console.log('新建 :>> ', key);
    if (key === 'addVolume') {
      setIsCreateVolumeModalVisible(true);
    }
  }, []);

  // tab 切换 (暂只有一个)
  const onTabChange = useCallback(
    (tabkey) => {
      // changeUrlFun((preState) => ({...preState, tab: tabkey}));
    },
    [changeUrlFun]
  );

  const renameNameRules = useMemo(() => {
    const ruleInfo = ruleMapByCatalog[catalogType];
    return [
      {
        validator: async (_, value) => {
          // 校验特殊字符和长度限制
          if (!ruleInfo.rule.test(value)) {
            return Promise.reject(new Error(ruleInfo.text));
          }
          // 异步校验Volume名称是否重复，复用查询接口 silent模式
          const res = await http.getSchemaDetail(`${catalog}.${value}`, true);
          if (res.success && res.result?.schema?.id) {
            return Promise.reject(new Error(`该${MetaCnNameMap['Schema']}名称已存在，请重新输入`));
          }
          return Promise.resolve();
        }
      }
    ];
  }, [catalog, catalogType]);

  return (
    <div className="work-meta-schema-panel">
      <Loading loading={loading}>
        {/* 标题导航操作栏 */}
        <MetaActionBar
          catalog={catalog}
          icon={<IconSvg type="meta-schema" size={20} color="#fff" />}
          title={schema}
          dropdownMenu={dropdownMenu}
          onDropdownClick={onDropdownClick}
          createText="立即新建"
          createIcon={<OutlinedPlusNew width={16} height={16} />}
          createMenu={createMenu}
          onCreateClick={onCreateClick}
          createDisabled={catalogType === CatalogType.DORIS}
        />
        {/* Tabs */}
        <MetaTabs panesList={initialPanes} tab={tab} onTabChange={onTabChange} />
        {/* Tab-Panel 1 */}
        {tab === PanelEnum.DETAIL ? (
          <>
            <DescriptionEdit
              text={dataInfo?.schema?.comment || ''}
              onChangeText={onChangeDescript}
              hasEdit={catalog !== CatalogType.SYSTEM}
            />
            <InfoPanel infoList={infoList} title="基本信息" />
          </>
        ) : null}
      </Loading>
      {/** 重命名 Schema 弹窗 */}
      <ModalRename
        visible={showRenameModal}
        setVisible={setRenameModal}
        fullName={fullName}
        nowValue={schema}
        title="Schema"
        requestFun={http.patchSchema}
        successFun={renameSuccessFun}
        nameRules={renameNameRules}
      />
      {/** 创建 Volume 弹窗 */}
      <CreateVolumeModal
        urlState={urlState}
        isModalVisible={isCreateVolumeModalVisible}
        handleCloseModal={() => setIsCreateVolumeModalVisible(false)}
        createdCallback={(name) => {
          changeUrlFun((pre) => ({...pre, type: EnumNodeType.VOLUME, node: name}));
          handleTreeRefresh && handleTreeRefresh();
        }}
      />
    </div>
  );
};
export default PanelSchema;
