import {useCallback, useEffect, useMemo, useState} from 'react';
import {Search, Table} from 'acud';
import {useAsyncEffect, useRequest} from 'ahooks';

import DescriptionEdit from '@components/DescriptionEdit';
import InfoPanel from '@components/InfoPanel';
import IconSvg from '@components/IconSvg';

import * as http from '@api/metaRequest';
import {IUrlStateHandler} from '../index';

import MetaActionBar from '../components/MetaActionBar';
import MetaTabs from '../components/MetaTabs';
import ModalRemoveFun from '../components/ModalRemoveFun';
import ModalRename from '../components/ModalRename';
import {RULE} from '@utils/regs';
import {MetaCnNameMap, ruleMapByCatalog} from '../config';
import {CatalogType} from '@api/metaRequest';

enum PanelEnum {
  OVERVIEW = '1',
  DETAIL = '2'
}

const initialPanes = [
  {tab: '概览', key: PanelEnum.OVERVIEW},
  {tab: '详情', key: PanelEnum.DETAIL}
];

const columns = [
  {
    title: '字段名称',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: '字段类型',
    dataIndex: 'typeName',
    key: 'typeName'
  },
  {
    title: '字段描述',
    dataIndex: 'comment',
    key: 'comment',
    render: (text) => text || '-'
  }
];

const PanelTable = (props: IUrlStateHandler) => {
  const {urlState, changeUrlFun, changeUrlFunReplace, userList, canWrite} = props;
  const {catalog = '', schema = '', node = '', tab = PanelEnum.OVERVIEW} = urlState;

  // catalog 类型
  const [catalogType, setCatalogType] = useState<CatalogType>(CatalogType.SYSTEM);

  const dropdownMenu = useMemo(
    () => [
      {key: 'rename', label: `重命名${MetaCnNameMap['Table']}`, disable: !canWrite},
      {key: 'remove', label: `删除${MetaCnNameMap['Table']}`, disable: !canWrite}
    ],
    [canWrite]
  );

  // 获取工作空间ID
  // const {workspaceId} = useContext(WorkspaceContext);
  // table 全名
  const fullName = `${catalog}.${schema}.${node}`;

  // 详情 & 概览
  const [dataInfo, setDataInfo] = useState<http.ITableDetailRes>();

  // 详情字段
  const infoList = useMemo(() => {
    const info: any = dataInfo || {};
    const isDoris = catalogType === CatalogType.DORIS;
    return [
      {
        label: `${MetaCnNameMap['Catalog']}名称`,
        value: info.catalogName
      },
      {
        label: `${MetaCnNameMap['Schema']}名称`,
        value: info.schemaName
      },
      {
        label: `${MetaCnNameMap['Table']}名称`,
        value: info.name
      },
      {
        label: '表类型',
        value: info.tableType
      },
      {
        label: '数据源格式',
        value: info.dataSourceFormat
      },
      ...(isDoris
        ? []
        : [
            {
              label: '存储路径',
              value: info.storageLocation
            }
          ]),
      {
        label: '创建时间',
        value: info.createdAt
      },
      ...(isDoris
        ? []
        : [
            {
              label: '创建人',
              value: userList.find((item) => item.id === info.createdBy)?.name || info.createdBy
            },
            {
              label: '修改时间',
              value: info.updatedAt
            },
            {
              label: '最近修改人',
              value: userList.find((item) => item.id === info.updatedBy)?.name || info.updatedBy
            }
          ])
    ];
  }, [catalogType, dataInfo, userList]);

  // 获取详情
  const {loading, run: getTableDetail} = useRequest(
    async () => {
      // 获取 catalog 类型，根据类型获取详情展示形式，system 不需要请求
      if (catalog !== CatalogType.SYSTEM) {
        const catalogDetail = await http.getCatalogDetail(catalog);
        setCatalogType(catalogDetail?.result?.catalog?.type);
      }
      const res = await http.getTableDetail(fullName);
      setDataInfo(res.result);
    },
    {
      manual: true
    }
  );

  // 字段信息 表格的 Datasource
  const [filterColumnVal, setFilterColumnVal] = useState('');
  const tableData = useMemo(() => {
    const data = dataInfo?.columns || [];
    return data.filter((item) => ~item.name.indexOf(filterColumnVal));
  }, [dataInfo, filterColumnVal]);

  // 初始化
  useEffect(() => {
    catalog && schema && node && getTableDetail();
  }, [catalog, schema, node]);

  // tab 切换
  const onTabChange = useCallback(
    (tabkey) => {
      changeUrlFun((preState) => ({...preState, tab: tabkey}));
    },
    [changeUrlFun]
  );

  // 更新描述
  const onChangeDescript = useCallback(
    async (text: string) => {
      await http.patchTable(fullName, {comment: text});
      getTableDetail();
    },
    [dataInfo, setDataInfo]
  );

  // 重命名 & 删除
  const [showRenameModal, setRenameModal] = useState<boolean>(false);
  const renameSuccessFun = useCallback(
    (formData: {name: string}) => {
      changeUrlFunReplace((preState) => ({...preState, node: formData.name}), true);
    },
    [changeUrlFunReplace]
  );
  const removeSuccessFun = useCallback(() => {
    changeUrlFunReplace((preState) => ({...preState, node: '', type: ''}), true);
  }, [changeUrlFunReplace]);
  const onDropdownClick = useCallback(
    (key) => {
      if (key === 'rename') {
        setRenameModal(true);
      } else if (key === 'remove') {
        ModalRemoveFun({
          fullName,
          name: node,
          title: MetaCnNameMap['Table'],
          requestFun: http.deleteTable,
          successFun: removeSuccessFun
        });
      }
    },
    [node, removeSuccessFun]
  );

  // 新建
  const onCreateClick = useCallback(() => {
    console.log('新建 :>> 一期不支持创建 Table');
  }, []);

  const renameNameRules = useMemo(() => {
    const ruleInfo = ruleMapByCatalog[catalogType];
    return [
      {
        validator: async (_, value) => {
          // 校验特殊字符和长度限制
          if (!ruleInfo.rule.test(value)) {
            return Promise.reject(new Error(ruleInfo.text));
          }
          // 异步校验Volume名称是否重复，复用查询接口 silent模式
          const res = await http.getTableDetail(`${catalog}.${schema}.${value}`, true);
          if (res.success && res.result?.id) {
            return Promise.reject(new Error(`该${MetaCnNameMap['Table']}名称已存在，请重新输入`));
          }
          return Promise.resolve();
        }
      }
    ];
  }, [catalog, catalogType, schema]);

  return (
    <div className="work-meta-table-panel">
      {/* 标题导航操作栏 */}
      <MetaActionBar
        catalog={catalog}
        icon={<IconSvg type="meta-table" size={20} color="#fff" />}
        title={node as string}
        dropdownMenu={dropdownMenu}
        onDropdownClick={onDropdownClick}
        hiddenCreate
        // createText="新建 Table"
        // onCreateClick={onCreateClick}
      />
      {/* Tabs */}
      <MetaTabs panesList={initialPanes} tab={tab} onTabChange={onTabChange} />
      {/* Tab-Panel 1 概览 */}
      {tab === PanelEnum.OVERVIEW ? (
        <div>
          <DescriptionEdit
            text={dataInfo?.comment || ''}
            onChangeText={onChangeDescript}
            hasEdit={catalog !== CatalogType.SYSTEM}
          />
          <h2 className="title-head">字段信息</h2>
          <Search
            className="table-overview-search"
            onChange={(e) => setFilterColumnVal(e.target.value)}
            allowClear
            placeholder="请输入字段名称查询"
          />
          <Table dataSource={tableData} columns={columns} loading={loading} />
        </div>
      ) : null}
      {/* Tab-Panel 2 详情 */}
      {tab === PanelEnum.DETAIL ? (
        <>
          <InfoPanel infoList={infoList} title="基本信息" />
        </>
      ) : null}
      {/** 重命名 Table 弹窗 */}
      <ModalRename
        visible={showRenameModal}
        setVisible={setRenameModal}
        fullName={fullName}
        nowValue={node}
        title="Table"
        requestFun={http.patchTable}
        successFun={renameSuccessFun}
        nameRules={renameNameRules}
      />
    </div>
  );
};
export default PanelTable;
