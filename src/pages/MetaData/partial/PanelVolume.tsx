import {ReactElement, useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {Upload as UploadIcon} from '@baidu/xicon-react-bigdata';
import {Button, Form, Input, Modal, toast, Upload} from 'acud';
import {OutlinedUpload} from 'acud-icon';
import {debounce} from 'lodash';
import cx from 'classnames';

import DescriptionEdit from '@components/DescriptionEdit';
import InfoPanel from '@components/InfoPanel';
import ShowUploadFile, {ShowUploadFileRefHandle} from '@components/ShowUploadFile';
import IconSvg from '@components/IconSvg';
import {useDrop} from 'ahooks';

import * as http from '@api/metaRequest';
import {IUrlStateHandler} from '../index';

import MetaActionBar from '../components/MetaActionBar';
import MetaTabs from '../components/MetaTabs';
import ModalRemoveFun from '../components/ModalRemoveFun';
import ModalRename from '../components/ModalRename';

import VolumeFileInfoTable, {VolumeFileRefHandle} from '../components/VolumeFileInfoTable';
import {RULE} from '@utils/regs';
import {TaskQueue} from '@utils/taskQueue';
import {OutlinedButtonUpload} from 'acud-icon';
import {validatePath} from '@utils/utils';
import {CatalogType} from '@api/metaRequest';

enum PanelEnum {
  OVERVIEW = '1',
  DETAIL = '2'
}

const initialPanes = [
  {tab: '概览', key: PanelEnum.OVERVIEW},
  {tab: '详情', key: PanelEnum.DETAIL}
];

const PanelVolume = (props: IUrlStateHandler) => {
  const {urlState, changeUrlFun, changeUrlFunReplace, handleTreeRefresh, userList, canWrite} = props;
  const {catalog = '', schema = '', node = '', tab = PanelEnum.OVERVIEW, path = ''} = urlState;

  const queue = new TaskQueue(2);

  const [controller, setController] = useState(new AbortController());

  const uploaderRef = useRef<ShowUploadFileRefHandle>();

  const dropdownMenu = useMemo(
    () => [
      {key: 'rename', label: '重命名 Volume', disable: !canWrite},
      {key: 'remove', label: '删除 Volume', disable: !canWrite}
    ],
    [canWrite]
  );

  // 获取工作空间ID
  // const {workspaceId} = useContext(WorkspaceContext);
  // volume 全名
  const fullName = `${catalog}.${schema}.${node}`;

  // 虚拟路径
  const volumePath = `/Volumes/${catalog}/${schema}/${node}/`;

  // 文件信息 Table 组件的 Ref
  const fileInfoTableRef = useRef<VolumeFileRefHandle>(null);

  // 详情 & 概览
  const [dataInfo, setDataInfo] = useState<http.IVolumeDetailRes>();

  // 控制文件夹模式
  const [isDir, setIsDir] = useState(true);
  const dropRef = useRef(null);

  useDrop(document.querySelector('.meta-volume-upload-box .acud-upload-list'), {
    onDrop: (e) => {
      e.preventDefault();
      e.stopPropagation();
      if (e.dataTransfer) {
        const newEvent = new DragEvent('drop', {
          bubbles: true,
          cancelable: true,
          clientX: e.clientX,
          clientY: e.clientY,
          screenX: e.screenX,
          screenY: e.screenY,
          dataTransfer: e.dataTransfer
        });
        document.querySelector('.meta-volume-upload-box .drag-content')?.dispatchEvent(newEvent);
      }
    }
  });

  const [form] = Form.useForm();

  // 详情字段
  const infoList = useMemo(() => {
    const info: any = dataInfo || {};
    return [
      {
        label: 'Catalog名称',
        value: info.catalogName
      },
      {
        label: 'Schema名称',
        value: info.schemaName
      },
      {
        label: 'Volume名称',
        value: info.name
      },
      {
        label: 'Volume类型',
        value: info.volumeType
      },
      {
        label: '存储路径',
        value: info.storageLocation
      },
      {
        label: '创建时间',
        value: info.createdAt
      },
      {
        label: '创建人',
        value: userList.find((item) => item.id === info.createdBy)?.name || info.createdBy
      },
      {
        label: '修改时间',
        value: info.updatedAt
      },
      {
        label: '最近修改人',
        value: userList.find((item) => item.id === info.updatedBy)?.name || info.updatedBy
      }
    ];
  }, [dataInfo, userList]);

  // 获取详情
  const getVolumeDetail = useCallback(async () => {
    const res = await http.getVolumeDetail(fullName);
    setDataInfo(res.result);
  }, [catalog, schema, node, form, volumePath]);

  // 初始化
  useEffect(() => {
    if (!(catalog && schema && node)) {
      return;
    }
    getVolumeDetail();
    uploaderRef.current?.close();
  }, [catalog, schema, node, getVolumeDetail]);

  useEffect(() => {
    return () => {
      controller.abort();
    };
  }, []);

  // tab 切换
  const onTabChange = useCallback(
    (tabkey) => {
      changeUrlFun((preState) => ({...preState, tab: tabkey}));
    },
    [changeUrlFun]
  );

  // 更新描述
  const onChangeDescript = useCallback(
    async (text: string) => {
      await http.patchVolume(fullName, {comment: text});
      getVolumeDetail();
    },
    [catalog, schema, node, getVolumeDetail]
  );

  // 重命名 & 删除
  const [showRenameModal, setRenameModal] = useState<boolean>(false);
  const renameSuccessFun = useCallback(
    (formData: {name: string}) => {
      changeUrlFunReplace((preState) => ({...preState, node: formData.name}), true);
    },
    [changeUrlFunReplace]
  );
  const removeSuccessFun = useCallback(() => {
    changeUrlFunReplace((preState) => ({...preState, node: '', type: ''}), true);
  }, [changeUrlFunReplace]);
  const onDropdownClick = useCallback(
    (key) => {
      if (key === 'rename') {
        setRenameModal(true);
      } else if (key === 'remove') {
        ModalRemoveFun({
          fullName,
          name: node,
          title: 'Volume',
          requestFun: http.deleteVolume,
          successFun: removeSuccessFun
        });
      }
    },
    [node, removeSuccessFun]
  );

  // 新建 Volume
  const [showCreateModal, setCreateModal] = useState<boolean>(false);
  const [fileList, setFileList] = useState<any[]>([]);
  const [uploadRequestInfo, setUploadRequestInfo] = useState<any>([]);

  const UploadProps = {
    onRemove: (file) => {
      const index = fileList.indexOf(file);
      const newFileList = fileList.slice();
      newFileList.splice(index, 1);
      setFileList(newFileList);
    },
    beforeUpload: (file) => {
      if (!validatePath(file.name)) {
        toast.error({
          message: '文件名称长度必须在1～1024字节之间，不能以/或者\\字符开头，不能出现连续的//',
          duration: 5
        });
        return Upload.LIST_IGNORE;
      }
      const isLt100M = file.size <= 1024 * 1024 * 100;
      const totalSize = fileList.reduce((sum, item) => sum + item.size, 0) + file.size;
      const isLt500M = totalSize <= 1024 * 1024 * 500;
      if (!isLt100M) {
        toast.error({
          message: '单个文件要小于100MB',
          duration: 5
        });
        return Upload.LIST_IGNORE;
      }
      if (!isLt500M) {
        toast.error({
          message: '单次上传文件总大小不能超过500MB',
          duration: 5
        });
        return Upload.LIST_IGNORE;
      }
      if (fileList.length === 100) {
        toast.error({
          message: '最多上传100个文件',
          duration: 5
        });
        return Upload.LIST_IGNORE;
      }
      setFileList((prevList) => [...prevList, file]);
      return false;
    },
    fileList
  };

  // 上传到内存
  const handleChange = (info) => {
    setFileList(info.fileList);
  };

  // 如果 Tab 页在概览页签，则刷新 「文件信息」列表
  const updataFilelist = debounce(() => {
    fileInfoTableRef?.current?.requestFilelist();
  }, 100);

  const getDirectoryPath = (file: any) => {
    const filePath = file?.originFileObj?.webkitRelativePath;
    const lastIndex = filePath.lastIndexOf('/');
    return ~lastIndex ? filePath.slice(0, lastIndex + 1) : '';
  };

  // 单个文件发起请求上传到后端
  const uploadSingleFile = useCallback(
    async (file: any) => {
      const formData = new FormData();
      formData.set('file', file.originFileObj, file.name);

      setUploadRequestInfo((pre) => {
        return pre.map((item) => {
          if (file.originFileObj.uid === item.file.uid) {
            return {...item, status: 'loading'};
          }
          return item;
        });
      });

      const directoryPath = getDirectoryPath(file);

      try {
        const res = await http.uploadVolumeFile(
          fullName,
          formData as any,
          volumePath + path + directoryPath,
          (progressEvent) => {
            setUploadRequestInfo((pre) => {
              return pre.map((item) => {
                if (file.originFileObj.uid === item.file.uid) {
                  return {...item, percent: Math.round((progressEvent.loaded / progressEvent.total) * 100)};
                }
                return item;
              });
            });
          },
          controller
        );
        if (!res.success) {
          throw new Error('Upload failed');
        }
        // 处理文件上传成功，更新 uploadRequestInfo中对应项的 status
        setUploadRequestInfo((pre) => {
          return pre.map((item) => {
            if (file.originFileObj.uid === item.file.uid) {
              return {...item, status: 'success'};
            }
            return item;
          });
        });
      } catch {
        // 处理文件上传失败，更新 uploadRequestInfo中对应项的 status
        setUploadRequestInfo((pre) => {
          return pre.map((item) => {
            if (file.originFileObj.uid === item.file.uid) {
              return {...item, status: 'failed'};
            }
            return item;
          });
        });
      }
      updataFilelist();
    },
    [dataInfo, updataFilelist, node, controller]
  );

  // 上传弹窗关闭
  const onCancelCreate = () => {
    setCreateModal(false);
    // 清空 fileList
    setFileList([]);
  };

  // 上传到后端
  const onCreateClick = useCallback(async () => {
    if (!fileList.length) {
      return;
    }
    const arr: any = [];
    fileList.map((item) => {
      const directoryPath = getDirectoryPath(item);

      arr.push({
        file: item.originFileObj,
        path: volumePath + path + directoryPath,
        status: 'loading'
      });
    });
    setUploadRequestInfo((pre) => [...pre, ...arr]);

    for (const file of fileList) {
      queue.add(async () => await uploadSingleFile(file));
    }

    uploaderRef?.current?.expand();

    onCancelCreate();
  }, [fileList, uploadRequestInfo, dataInfo, uploadSingleFile]);

  return (
    <div className="work-meta-volume-panel">
      {/* 标题导航操作栏 */}
      <MetaActionBar
        catalog={catalog}
        icon={<IconSvg type="meta-volume" size={20} color="#fff" />}
        title={node as string}
        dropdownMenu={dropdownMenu}
        onDropdownClick={onDropdownClick}
        createText="上传数据到 Volume"
        createIcon={<OutlinedButtonUpload />}
        onCreateClick={() => setCreateModal(true)}
      />
      {/* Tabs */}
      <MetaTabs panesList={initialPanes} tab={tab} onTabChange={onTabChange} />
      {/* Tab-Panel 1 概览 */}
      {tab === PanelEnum.OVERVIEW ? (
        <div>
          <DescriptionEdit
            text={dataInfo?.comment || ''}
            onChangeText={onChangeDescript}
            hasEdit={catalog !== CatalogType.SYSTEM}
          />
          <h2 className="title-head">文件信息</h2>
          <div>
            <VolumeFileInfoTable
              ref={fileInfoTableRef}
              dataInfo={dataInfo}
              urlState={urlState}
              changeUrlFun={changeUrlFun}
            />
          </div>
        </div>
      ) : null}
      {/* Tab-Panel 2 详情 */}
      {tab === PanelEnum.DETAIL ? <InfoPanel infoList={infoList} title="基本信息" /> : null}
      {/** 重命名 Volume 弹窗 */}
      <ModalRename
        visible={showRenameModal}
        setVisible={setRenameModal}
        fullName={fullName}
        nowValue={node}
        title="Volume"
        requestFun={http.patchVolume}
        successFun={renameSuccessFun}
        limitLength={64}
        forbidIfLimit
        nameRules={[
          {
            validator: async (_, value) => {
              // 校验特殊字符和长度限制
              if (!RULE.specialName64.test(value)) {
                return Promise.reject(new Error(RULE.specialName64Text));
              }
              // 异步校验Volume名称是否重复，复用查询接口 silent模式
              const res = await http.getVolumeDetail(`${catalog}.${schema}.${value}`, true);
              if (res.success && res.result?.id) {
                return Promise.reject(new Error('该Volume名称已存在，请重新输入'));
              }
              return Promise.resolve();
            }
          }
        ]}
      />
      {/* 上传 Volume 弹窗 */}
      <Modal
        closable={true}
        title="将⽂件上传到Volume"
        okText="上传"
        size="normal"
        visible={showCreateModal}
        okButtonProps={{
          disabled: fileList.length === 0
        }}
        onOk={onCreateClick}
        onCancel={onCancelCreate}
      >
        <div className="meta-volume-upload-box">
          <Upload ref={dropRef} {...UploadProps} multiple onChange={handleChange} directory={isDir}>
            <div
              className="drag-content"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                setIsDir(false);
                setTimeout(() => {
                  dropRef.current?.upload?.uploader?.onClick();
                  setIsDir(true);
                }, 10);
              }}
            >
              {fileList.length ? (
                <div className="volume-upload-text volume-upload-text-extra">
                  将文件夹或文件拖拽到文件列表区域可继续添加，也可<Button type="actiontext">点击此处</Button>
                  选择文件
                </div>
              ) : (
                <>
                  <OutlinedUpload fill="#D3D3D3" width={40} />
                  <div className="volume-upload-text">
                    将文件夹或多个文件拖到此处，或
                    <Button type="actiontext">点击上传</Button>
                  </div>
                  <p className="volume-upload-des">
                    * ⽬标⽬录下如果存在同名⽂件，将被新上传的⽂件覆盖
                    <br />* 每次上传的总文件大小不超过500MB，单个文件不超过100MB；每次最多上传100个文件
                  </p>
                </>
              )}
            </div>
          </Upload>
        </div>
        <Form
          className="meta-volume-upload-form"
          labelAlign="left"
          layout="vertical"
          inputMaxWidth="758px"
          name="nest-messages"
        >
          <Form.Item label="上传目录">
            <Input disabled value={volumePath + path} />
          </Form.Item>
        </Form>
      </Modal>
      {/* 上传文件展示组件 */}
      <ShowUploadFile
        ref={uploaderRef}
        uploadList={uploadRequestInfo}
        setUploadListFun={setUploadRequestInfo}
        reUploadRequest={uploadSingleFile}
        afterCloseFn={() => {
          controller.abort();
          setController(new AbortController());
        }}
      />
    </div>
  );
};
export default PanelVolume;
