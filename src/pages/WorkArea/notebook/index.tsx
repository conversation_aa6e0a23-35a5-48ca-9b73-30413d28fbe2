import {useState, useEffect, useContext, useRef} from 'react';
import Header from './components/Header';
import Toolbar from './components/Toolbar';
import NotebookEditor from './components/NotebookEditor';
import LeftSidebar from './components/LeftSidebar';
import styles from './index.module.less';
import classNames from 'classnames/bind';
import {getNotebookContent} from '@api/WorkArea';
import useUrlState from '@ahooksjs/use-url-state';
import {INotebookContent} from '@jupyterlab/nbformat';
import {WorkspaceContext} from '@pages/index';
import {useRequest} from 'ahooks';
import {Loading, DialogBox, Button} from 'acud';
import {useNotebookUnsavedPrompt, useNotebookResize, useNotebookSave} from './hook';

const cx = classNames.bind(styles);

export default function NotebookEdit() {
  const [urlState, setUrlState] = useUrlState();
  const [nbformat, setNbformat] = useState<INotebookContent | null>(null);
  const [notebookName, setNotebookName] = useState('');
  const {workspaceId} = useContext(WorkspaceContext);
  const notebookId = urlState.notebookId;

  // 未保存提示
  const {blocker} = useNotebookUnsavedPrompt(notebookId);

  // 动态更新notebook宽度
  const nbRef = useRef<HTMLDivElement>(null);
  useNotebookResize(nbRef, notebookId, nbformat);

  const {run: getNotebookContentRun, loading} = useRequest(getNotebookContent, {
    manual: true,
    onSuccess: (res) => {
      if (res.success && res.result) {
        setNbformat(res.result.content);
        setNotebookName(res.result.name);
      }
    }
  });

  useEffect(() => {
    if (urlState.notebookId && workspaceId) {
      getNotebookContentRun({id: urlState.notebookId, workspaceId});
    }
  }, [urlState.notebookId, workspaceId, getNotebookContentRun]);

  const [visible, setVisible] = useState<boolean>(false);
  useEffect(() => {
    if (blocker.state === 'blocked') {
      setVisible(true);
    }
  }, [blocker.state]);

  const {save, saveLoading} = useNotebookSave(notebookId, workspaceId);
  const exitAndSave = async () => {
    await save();
    setVisible(false);
    blocker.proceed();
  };

  const exitText = `当前Notebook"${notebookName}"暂未保存，如果现在关闭，未保存的数据内容将丢失。请确认是否 要继续关闭`;

  return (
    <div className={cx('db-workspace-wrapper', 'notebook-wrapper')}>
      {loading ? (
        <Loading loading />
      ) : (
        <>
          <Header notebookId={notebookId} notebookName={notebookName} onNameChange={setNotebookName} />
          <Toolbar notebookId={notebookId} notebookName={notebookName} />
          <div className={cx('flex', 'notebook-content')}>
            <div className={cx('flex-none')}>
              <LeftSidebar />
            </div>
            <div className={cx('flex-1')} ref={nbRef}>
              {nbformat && <NotebookEditor id={notebookId} nbformat={nbformat} />}
            </div>
          </div>
        </>
      )}
      <DialogBox
        visible={visible}
        title="未保存"
        content={exitText}
        type="warning"
        footer={
          <div>
            <Button
              onClick={() => {
                blocker.reset();
                setVisible(false);
              }}
            >
              取消
            </Button>
            <Button
              onClick={() => {
                blocker.proceed();
                setVisible(false);
              }}
            >
              立即退出
            </Button>
            <Button type="primary" onClick={exitAndSave} loading={saveLoading} disabled={saveLoading}>
              保存并退出
            </Button>
          </div>
        }
      ></DialogBox>
    </div>
  );
}
