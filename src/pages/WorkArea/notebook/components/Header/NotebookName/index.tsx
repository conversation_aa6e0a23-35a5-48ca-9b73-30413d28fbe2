import React, {useState, useRef, useEffect} from 'react';
import {Tooltip} from 'acud';
import {validatePath, FILE_NAME_ERROR_MESSAGE} from '@utils/utils';
import classNames from 'classnames/bind';
import styles from './index.module.less';

const cx = classNames.bind(styles);

interface NotebookNameProps {
  name: string;
  onNameChange?: (newName: string) => void;
}

const NotebookName: React.FC<NotebookNameProps> = ({name, onNameChange}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [inputValue, setInputValue] = useState(name);
  const [error, setError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);

  // 进入编辑模式时自动聚焦输入框
  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  // 校验输入名称
  const validateInput = (value: string) => {
    const trimmedValue = value.trim();
    if (!trimmedValue) {
      setError(true);
      setErrorMessage('名称不能为空');
      return false;
    }

    if (!validatePath(trimmedValue)) {
      setError(true);
      setErrorMessage(FILE_NAME_ERROR_MESSAGE);
      return false;
    }

    setError(false);
    setErrorMessage('');
    return true;
  };

  // 处理名称保存
  const handleSave = () => {
    const trimmedValue = inputValue.trim();

    // 校验名称合法性
    if (!validateInput(trimmedValue)) {
      return; // 校验不通过，不退出编辑状态
    }

    if (trimmedValue !== name && onNameChange) {
      // 只有当名称发生变化时才触发回调
      onNameChange(trimmedValue);
    }
    setIsEditing(false);
  };

  // 处理按键事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSave();
    } else if (e.key === 'Escape') {
      // 取消编辑，恢复原名称
      setInputValue(name);
      setError(false);
      setErrorMessage('');
      setIsEditing(false);
    }
  };

  return (
    <div className={cx('notebook-name')}>
      {isEditing ? (
        <div className={cx('input-container')}>
          <input
            ref={inputRef}
            className={cx('name-input', {error: error})}
            value={inputValue}
            onChange={(e) => {
              setInputValue(e.target.value);
              validateInput(e.target.value);
            }}
            onBlur={() => {
              if (!error) {
                handleSave();
              }
            }}
            onKeyDown={handleKeyDown}
            maxLength={50}
          />
          {error && errorMessage && <div className={cx('error-message')}>{errorMessage}</div>}
        </div>
      ) : (
        <Tooltip title={name} placement="bottom">
          <div className={cx('name-display')} onClick={() => setIsEditing(true)} title="点击编辑名称">
            {name}
          </div>
        </Tooltip>
      )}
    </div>
  );
};

export default NotebookName;
