import {useState, useContext, useEffect, useRef} from 'react';
import {Button, DialogBox} from 'acud';
import classNames from 'classnames/bind';
import styles from './index.module.less';
import {useNotebookAction} from '../../hook';
import {useNavigate} from 'react-router-dom';
import urls from '@utils/urls';
import useUrlState from '@ahooksjs/use-url-state';
import {WorkspaceContext} from '@pages/index';
import ComputeSelector from './ComputeSelector';
import NotebookName from './NotebookName';
import {useNotebookStore} from '@baidu/db-jupyter-react/lib/components/notebook';
import {useNotebook} from '@store/notebookStatehooks';

const cx = classNames.bind(styles);

export default function Header({
  notebookId,
  notebookName,
  onNameChange
}: {
  notebookId: string;
  notebookName: string;
  onNameChange?: (newName: string) => void;
}) {
  const navigate = useNavigate();
  const {
    runAll: onRun,
    save,
    saveLoading,
    updateNotebookName,
    updateSessionFileName
  } = useNotebookAction(notebookId);
  const {connectStatus} = useNotebook();

  const [visible, setVisible] = useState(false);
  const [urlState] = useUrlState();
  const {workspaceId} = useContext(WorkspaceContext);
  const [currentName, setCurrentName] = useState(notebookName);
  const notebookStore = useNotebookStore();
  const notebook = notebookStore.selectNotebook(notebookId);

  // 第一次渲染时，将 notebook 的 dirty 置为 false
  const firstRender = useRef(true);
  useEffect(() => {
    if (!firstRender.current) {
      return;
    }
    if (notebook?.adapter?.notebookPanel.model) {
      setTimeout(() => {
        notebook.adapter.notebookPanel.model.dirty = false;
        firstRender.current = false;
      }, 100);
    }
  }, [notebook]);

  // 当 notebookName 从父组件更新时，更新 currentName
  useEffect(() => {
    if (notebookName) {
      setCurrentName(notebookName);
    }
  }, [notebookName]);

  const exitText = `当前Notebook"${currentName}"暂未保存，如果现在关闭，未保存的数据内容将丢失。请确认是否 要继续关闭`;

  const goWorkArea = () => {
    navigate(`${urls.workArea}?workspaceId=${workspaceId}&folderId=${urlState.folderId}`);
  };

  const exit = () => {
    setVisible(false);
    goWorkArea();
  };

  const exitAndSave = async () => {
    if (saveLoading) {
      return;
    }
    await save();
    setVisible(false);
    goWorkArea();
  };

  // 处理名称变更
  const handleNameChange = async (newName: string) => {
    if (newName !== currentName) {
      try {
        await updateNotebookName(newName);
        setCurrentName(newName);
        // 更新 session 名称
        updateSessionFileName(newName);
        onNameChange?.(newName);
      } catch (error) {
        console.error('更新笔记本名称失败:', error);
      }
    }
  };

  return (
    <div className={cx('header')}>
      <div className={cx('title')}>
        <NotebookName name={currentName} onNameChange={handleNameChange} />
      </div>
      <div className={cx('actions')}>
        <div className={cx('action-item')}>
          <ComputeSelector />
        </div>
        <div className={cx('action-item')}>
          <Button type="primary" onClick={onRun} disabled={notebook?.connectStatus !== 'CONNECTED'}>
            <span className={cx('run-icon')}></span>
            <span>全部运行</span>
          </Button>
        </div>
        <div className={cx('action-item')}>
          <Button
            onClick={() => {
              goWorkArea();
            }}
          >
            退出
          </Button>
        </div>
      </div>
      {/* <DialogBox
        visible={visible}
        title="是否退出"
        content={exitText}
        type="warning"
        onCancel={() => setVisible(false)}
        footer={
          <div>
            <Button onClick={() => setVisible(false)}>取消</Button>
            <Button onClick={exit}>立即退出</Button>
            <Button type="primary" onClick={exitAndSave} loading={saveLoading} disabled={saveLoading}>
              保存并退出
            </Button>
          </div>
        }
      ></DialogBox> */}
    </div>
  );
}
