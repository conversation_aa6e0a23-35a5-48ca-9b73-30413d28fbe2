import React, {useState} from 'react';
import classNames from 'classnames/bind';
import styles from './index.module.less';
import MetaDataTree from './components/MetaDataTree';
import HotkeysHelp from './components/HotkeysHelp';

const cx = classNames.bind(styles);

export default function LeftSidebar() {
  const [activeKey, setActiveKey] = useState('');

  // 快捷键帮助弹窗状态
  const [hotkeysHelpVisible, setHotkeysHelpVisible] = useState(false);

  const sidebarItems = [
    {
      key: 'meta-data',
      label: '元数据'
    }
  ];

  function onIconClick(key: string) {
    setActiveKey((pre) => (pre === key ? '' : key));
  }

  return (
    <div className={cx('left-sidebar')}>
      <div className={cx('sidebar-icons-wrap')}>
        <div className={cx('sidebar-icons')}>
          {sidebarItems.map((item) => {
            return (
              <div
                className={cx('sidebar-icon', item.key, {active: activeKey === item.key})}
                key={item.key}
                onClick={() => onIconClick(item.key)}
              ></div>
            );
          })}
        </div>
        <div
          className={cx('sidebar-icon', 'sidebar-keymap')}
          onClick={() => setHotkeysHelpVisible(true)}
        ></div>
      </div>
      <div className={cx('sidebar-content', {hide: !activeKey})}>
        {activeKey === 'meta-data' ? <MetaDataTree onClose={() => setActiveKey('')} /> : null}
      </div>
      <HotkeysHelp visible={hotkeysHelpVisible} onClose={() => setHotkeysHelpVisible(false)} />
    </div>
  );
}
