import './public-path';

import 'acud/dist/acud.min.css';
import '@baidu/bce-react-toolkit/es/styles/_overwrite_acud.css';

import '@styles/global.less';
import '@styles/dbsc.less';
import '@styles/tailwind.css';
import '@styles/acud.less';

import {createRoot} from 'react-dom/client';
import {
  AppProvider,
  FrameworkProvider,
  i18nInstance,
  I18nProvider,
  I18nUtil,
  toolkitConfig
} from '@baidu/bce-react-toolkit';

import App from './App';

toolkitConfig.init({
  enableI18n: APP_ENABLE_I18N,
  enableIndependentI18n: APP_ENABLE_INDEPENDENT_I18N,
  isEmbed: APP_IS_EMBED_MODE,
  publicPath: window.appPublicPath,
  supportedLanguageTypes: APP_ALLOWED_LANGUAGE_TYPES || [],
  appTitle: 'DataBuilder'
});

export async function bootstrap(initData: any) {
  const i18nUtil = new I18nUtil();
  await i18nUtil.init();

  // 获取根元素
  const container = document.querySelector('#main');

  // 创建根
  const root = createRoot(container as HTMLElement);

  // 使用 root.render 渲染应用
  root.render(
    <I18nProvider i18n={i18nInstance} defaultNS={'translation'} i18nUtil={i18nUtil}>
      <FrameworkProvider frameworkData={initData}>
        <AppProvider>
          <App />
        </AppProvider>
      </FrameworkProvider>
    </I18nProvider>
  );
}
