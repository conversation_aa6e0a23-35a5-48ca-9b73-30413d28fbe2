<!--
  ~ Copyright (c) 2021-2023 Datalayer, Inc.
  ~
  ~ MIT License
-->

<!--
  ~ Copyright (c) Jupyter Development Team.
  ~ Distributed under the terms of the Modified BSD License.
-->

<!DOCTYPE html>
<html>
<head>
  <title>{{page_config['appName'] | e}}</title>
  {# Copy so we do not modify the page_config with updates. #}
  {% set page_config_full = page_config.copy() %}

  {# Set a dummy variable - we just want the side effect of the update. #}
  {% set _ = page_config_full.update(baseUrl=base_url, wsUrl=ws_url) %}

  <script id="jupyter-config-data" type="application/json">
    {{ page_config_full | tojson }}
  </script>
  <script src="{{page_config['fullStaticUrl'] | e}}/bundle.js" main="index"></script>
</head>
<body>

  <script type="text/javascript">
    /* Remove token from URL. */
    (function () {
      var parsedUrl = new URL(window.location.href);
      if (parsedUrl.searchParams.get('token')) {
        parsedUrl.searchParams.delete('token');
        window.history.replaceState({ }, '', parsedUrl.href);
      }
    })();
  </script>
</body>
</html>
