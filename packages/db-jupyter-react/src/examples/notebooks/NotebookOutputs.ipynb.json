{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["2"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["1+1"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'HTML' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m display(\u001b[43mHTML\u001b[49m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m\"\u001b[39m));\n", "\u001b[0;31mNameError\u001b[0m: name 'HTML' is not defined"]}], "source": ["display(HTML(\"\"));"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {}, "nbformat": 4, "nbformat_minor": 4}