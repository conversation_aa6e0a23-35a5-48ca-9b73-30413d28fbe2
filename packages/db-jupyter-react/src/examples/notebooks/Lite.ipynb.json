{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Emscripten'"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import platform\n", "platform.system()"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["emscripten"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import sys\n", "sys.platform"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["<pyodide_kernel.interpreter.Interpreter at 0x22906f0>"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["get_ipython()"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["4"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["# I am a Python cell.\n", "print('2+2')"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": []}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import micropip\n", "await micropip.install('ipywidgets')"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": []}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from ipywidgets import IntSlider\n", "w = IntSlider()\n", "w"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": []}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["w"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {}, "nbformat": 4, "nbformat_minor": 4}