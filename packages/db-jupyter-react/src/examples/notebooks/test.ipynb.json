{"cells": [{"cell_type": "code", "source": "from IPython.display import Image, Video, Audio, HTML, Markdown, Math\n\n# 展示图片\ndisplay(Image('http://localhost:3208/pic.jpg'))\n\n# 展示视频\ndisplay(Video('http://localhost:3208/video.mp4'))\n", "metadata": {"editable": true, "trusted": true, "execution": {"iopub.status.busy": "2025-04-16T08:12:09.909534Z", "iopub.execute_input": "2025-04-16T08:12:09.910572Z", "iopub.status.idle": "2025-04-16T08:12:09.939204Z", "shell.execute_reply.started": "2025-04-16T08:12:09.910526Z", "shell.execute_reply": "2025-04-16T08:12:09.938776Z"}}, "outputs": [{"output_type": "display_data", "data": {"image/jpeg": "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*****************************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", "text/plain": "<IPython.core.display.Image object>"}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": "<IPython.core.display.Video object>", "text/html": "<video src=\"http://localhost:3208/video.mp4\" controls  >\n      Your browser does not support the <code>video</code> element.\n    </video>"}, "metadata": {}}], "execution_count": 17}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["hello world\n", "0\n", "1\n", "2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["output to stderr\n"]}, {"name": "stdout", "output_type": "stream", "text": ["some more stdout text\n"]}], "source": ["import sys\n", "sys.stdout.write('hello world\\n')\n", "sys.stdout.flush()\n", "for i in range(3):\n", "    sys.stdout.write('%s\\n' % i)\n", "    sys.stdout.flush()\n", "sys.stderr.write('output to stderr\\n')\n", "sys.stderr.flush()\n", "sys.stdout.write('some more stdout text\\n')\n", "sys.stdout.flush()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Markdown Cell\n", "\n", "$ e^{ \\pm i\\theta } = \\cos \\theta \\pm i\\sin \\theta + \\beta $\n", "\n", "*It* **really** is!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "SyntaxError", "evalue": "invalid syntax (2040198529.py, line 1)", "output_type": "error", "traceback": ["\u001b[0;36m  Input \u001b[0;32mIn [2]\u001b[0;36m\u001b[0m\n\u001b[0;31m    this is a syntax error\u001b[0m\n\u001b[0m              ^\u001b[0m\n\u001b[0;31mSyntaxError\u001b[0m\u001b[0;31m:\u001b[0m invalid syntax\n"]}], "source": ["this is a syntax error"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["test\n"]}], "source": ["print('test')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/latex": ["The mass-energy equivalence is described by the famous equation\n", " \n", "$$E=mc^2$$\n", " \n", "discovered in 1905 by <PERSON>. \n", "In natural units ($c$ = 1), the formula expresses the identity\n", " \n", "\\begin{equation}\n", "E=m\n", "\\end{equation}"], "text/plain": ["<IPython.core.display.Latex object>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from IPython.display import Latex\n", "Latex('''The mass-energy equivalence is described by the famous equation\n", " \n", "$$E=mc^2$$\n", " \n", "discovered in 1905 by <PERSON>. \n", "In natural units ($c$ = 1), the formula expresses the identity\n", " \n", "\\\\begin{equation}\n", "E=m\n", "\\\\end{equation}''')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6b433509d21d4de0860d6a65c9fc3031", "version_major": 2, "version_minor": 0}, "text/plain": ["IntSlider(value=0)"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b769f28cfced44fe971b97ae2876e865", "version_major": 2, "version_minor": 0}, "text/plain": ["IntText(value=0)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import display\n", "import ipywidgets as w\n", "# s = w.<PERSON><PERSON><PERSON><PERSON><PERSON>(0, 10)£\n", "# s\n", "a = w.<PERSON><PERSON>()\n", "b = w.IntText()\n", "w.jslink((a, 'value'), (b, 'value'))\n", "display(a, b)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"bingroup": "x", "histfunc": "sum", "hovertemplate": "sex=Female<br>total_bill=%{x}<br>sum of tip=%{y}<extra></extra>", "legendgroup": "Female", "marker": {"color": "#636efa", "pattern": {"shape": ""}}, "name": "Female", "orientation": "v", "showlegend": true, "type": "histogram", "x": {"bdata": "PQrXo3D9MEDXo3A9Cpc4QOF6FK5HoUFAKVyPwvWoLUApXI/C9agkQLgehetR+DBACtejcD1KNEAK16NwPYovQGZmZmZmpjNAH4XrUbgeLkBxPQrXo7A0QK5H4XoU7jBAFK5H4XqUJEBI4XoUrmdBQClcj8L1aDpAMzMzMzNzMECPwvUoXI8IQFK4HoXrETFAXI/C9SjcOkBI4XoUrkc5QPYoXI/CdS1ApHA9CtcjJEAK16NwPWpBQAAAAAAAABdAUrgehetRMEAAAAAAAMA2QDMzMzMzsyZAw/UoXI/CLkBmZmZmZiZGQOxRuB6FazZA7FG4HoXrNEAfhetRuJ4sQAAAAAAAAB1A9ihcj8K1OUCPwvUoXE8xQM3MzMzMTCVAXI/C9SjcKEAUrkfhehQ4QNejcD0K1ypA9ihcj8L1KEDNzMzMzMw9QArXo3A9Ci1Aw/UoXI/CJkCF61G4HkU0QNejcD0KVyZAhetRuB6FKEDD9Shcj0IyQIXrUbgeBSFAKVyPwvWoJEDNzMzMzEwsQFK4HoXrUSpAuB6F61F4MUDNzMzMzAw7QK5H4XoUbjBAMzMzMzOzIECkcD0K16MyQD0K16NwvSdAmpmZmZnZPUAAAAAAAAA5QEjhehSuxypA9ihcj8I1MEDD9Shcj4IxQK5H4XoULiVAw/UoXI9CJUAzMzMzMzMjQGZmZmZm5jRAZmZmZmYmMkCPwvUoXM8zQK5H4XoUjkVAAAAAAAAAKkB7FK5H4XopQAAAAAAAACpAZmZmZmZmMEC4HoXrUXgwQIXrUbgehSlACtejcD2KKkDsUbgehSs8QM3MzMzMzClApHA9CtcjPkDXo3A9CtcqQPYoXI/C9S9AhetRuB5FMECuR+F6FC4kQB+F61G4HjZACtejcD3qQUCuR+F6FC47QEjhehSuxzJA", "dtype": "f8"}, "xaxis": "x", "y": {"bdata": "KVyPwvUo8D/hehSuR+EMQAAAAAAAABRAKVyPwvUoCEC4HoXrUbj6PwAAAAAAAAxAAAAAAAAABkDXo3A9CtcBQAAAAAAAAAhAAAAAAAAACECamZmZmZkDQI/C9ShcjwhAzczMzMzMBEDNzMzMzMwUQAAAAAAAAPg/w/UoXI/CA0AAAAAAAADwPwAAAAAAAAhAH4XrUbgeCUAAAAAAAAAUQJqZmZmZmQFASOF6FK5H/T+uR+F6FK4UQAAAAAAAAPA/MzMzMzMzEUAAAAAAAAAKQAAAAAAAAARAAAAAAAAACEAAAAAAAAAEQNejcD0K1wtAUrgehetREEAAAAAAAAAQQAAAAAAAAPA/AAAAAAAAEEAAAAAAAAAMQAAAAAAAAPg/zczMzMzM/D9cj8L1KFwHQOF6FK5H4fo/KVyPwvUoBEDNzMzMzMwQQAAAAAAAAABAAAAAAAAAAECkcD0K16MGQAAAAAAAAPg/AAAAAAAAAEAAAAAAAAAKQAAAAAAAAPQ/AAAAAAAAAEAAAAAAAAAAQAAAAAAAAAZAAAAAAAAADEAAAAAAAAAUQGZmZmZmZgJAAAAAAAAA+D/D9Shcj8L1PxSuR+F6FPo/j8L1KFyPFEAAAAAAAAAOQOF6FK5H4QRAAAAAAAAAAEAAAAAAAAAIQMP1KFyPwvk/AAAAAAAAAEAAAAAAAAAQQAAAAAAAAAxAAAAAAAAADEDD9Shcj8IQQAAAAAAAABRAAAAAAAAAAEAUrkfhehQAQAAAAAAAAABAAAAAAAAABEDXo3A9CtcJQNejcD0K1wFAAAAAAAAABEAAAAAAAAAaQJqZmZmZmfE/uB6F61G4CEDXo3A9CtcLQAAAAAAAAAhAAAAAAAAABEAAAAAAAAAAQArXo3A9CgdArkfhehSuEkAAAAAAAAAAQAAAAAAAAAhA", "dtype": "f8"}, "yaxis": "y"}, {"boxpoints": "all", "customdata": [["Female", "No", "Sun", "Dinner", 2], ["Female", "No", "Sun", "Dinner", 4], ["Female", "No", "Sun", "Dinner", 4], ["Female", "No", "Sun", "Dinner", 2], ["Female", "No", "Sun", "Dinner", 3], ["Female", "No", "Sun", "Dinner", 3], ["Female", "No", "Sat", "Dinner", 2], ["Female", "No", "Sat", "Dinner", 2], ["Female", "No", "Sat", "Dinner", 2], ["Female", "No", "Sat", "Dinner", 2], ["Female", "No", "Sat", "Dinner", 4], ["Female", "No", "Sat", "Dinner", 3], ["Female", "No", "Sun", "Dinner", 2], ["Female", "No", "Sun", "Dinner", 4], ["Female", "No", "Sat", "Dinner", 2], ["Female", "No", "Sat", "Dinner", 2], ["Female", "Yes", "Sat", "Dinner", 1], ["Female", "No", "Sat", "Dinner", 3], ["Female", "Yes", "Sat", "Dinner", 2], ["Female", "Yes", "Sat", "Dinner", 2], ["Female", "No", "Sat", "Dinner", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 1], ["Female", "No", "<PERSON><PERSON>", "Lunch", 4], ["Female", "Yes", "<PERSON><PERSON>", "Dinner", 2], ["Female", "Yes", "<PERSON><PERSON>", "Dinner", 2], ["Female", "No", "<PERSON><PERSON>", "Dinner", 2], ["Female", "Yes", "<PERSON><PERSON>", "Dinner", 2], ["Female", "Yes", "<PERSON><PERSON>", "Dinner", 2], ["Female", "Yes", "Sat", "Dinner", 3], ["Female", "Yes", "Sat", "Dinner", 2], ["Female", "No", "Sat", "Dinner", 2], ["Female", "Yes", "Sat", "Dinner", 2], ["Female", "No", "Sat", "Dinner", 1], ["Female", "No", "Sun", "Dinner", 3], ["Female", "No", "Sun", "Dinner", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 4], ["Female", "No", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 6], ["Female", "No", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 6], ["Female", "No", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 3], ["Female", "No", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "Sun", "Dinner", 5], ["Female", "No", "Sun", "Dinner", 4], ["Female", "No", "Sun", "Dinner", 2], ["Female", "No", "Sun", "Dinner", 3], ["Female", "Yes", "Sun", "Dinner", 2], ["Female", "Yes", "Sat", "Dinner", 2], ["Female", "Yes", "Sat", "Dinner", 2], ["Female", "Yes", "Sun", "Dinner", 2], ["Female", "Yes", "Sun", "Dinner", 3], ["Female", "Yes", "Sun", "Dinner", 3], ["Female", "Yes", "<PERSON><PERSON>", "Lunch", 2], ["Female", "Yes", "<PERSON><PERSON>", "Lunch", 4], ["Female", "Yes", "<PERSON><PERSON>", "Lunch", 2], ["Female", "Yes", "<PERSON><PERSON>", "Lunch", 2], ["Female", "Yes", "<PERSON><PERSON>", "Lunch", 2], ["Female", "Yes", "<PERSON><PERSON>", "Lunch", 2], ["Female", "Yes", "<PERSON><PERSON>", "Lunch", 3], ["Female", "Yes", "Sat", "Dinner", 2], ["Female", "Yes", "Sat", "Dinner", 2], ["Female", "Yes", "Sat", "Dinner", 3], ["Female", "Yes", "Sat", "Dinner", 2], ["Female", "Yes", "Sat", "Dinner", 4], ["Female", "Yes", "<PERSON><PERSON>", "Lunch", 2], ["Female", "No", "<PERSON><PERSON>", "Lunch", 3], ["Female", "Yes", "<PERSON><PERSON>", "Lunch", 2], ["Female", "Yes", "<PERSON><PERSON>", "Lunch", 2], ["Female", "Yes", "Sat", "Dinner", 2], ["Female", "No", "Sat", "Dinner", 3], ["Female", "Yes", "Sat", "Dinner", 2], ["Female", "No", "<PERSON><PERSON>", "Dinner", 2]], "fillcolor": "rgba(255,255,255,0)", "hoveron": "points", "hovertemplate": "sex=%{customdata[0]}<br>total_bill=%{x}<br>smoker=%{customdata[1]}<br>day=%{customdata[2]}<br>time=%{customdata[3]}<br>size=%{customdata[4]}<extra></extra>", "jitter": 0, "legendgroup": "Female", "line": {"color": "rgba(255,255,255,0)"}, "marker": {"color": "#636efa", "symbol": "line-ns-open"}, "name": "Female", "showlegend": false, "type": "box", "x": {"bdata": "PQrXo3D9MEDXo3A9Cpc4QOF6FK5HoUFAKVyPwvWoLUApXI/C9agkQLgehetR+DBACtejcD1KNEAK16NwPYovQGZmZmZmpjNAH4XrUbgeLkBxPQrXo7A0QK5H4XoU7jBAFK5H4XqUJEBI4XoUrmdBQClcj8L1aDpAMzMzMzNzMECPwvUoXI8IQFK4HoXrETFAXI/C9SjcOkBI4XoUrkc5QPYoXI/CdS1ApHA9CtcjJEAK16NwPWpBQAAAAAAAABdAUrgehetRMEAAAAAAAMA2QDMzMzMzsyZAw/UoXI/CLkBmZmZmZiZGQOxRuB6FazZA7FG4HoXrNEAfhetRuJ4sQAAAAAAAAB1A9ihcj8K1OUCPwvUoXE8xQM3MzMzMTCVAXI/C9SjcKEAUrkfhehQ4QNejcD0K1ypA9ihcj8L1KEDNzMzMzMw9QArXo3A9Ci1Aw/UoXI/CJkCF61G4HkU0QNejcD0KVyZAhetRuB6FKEDD9Shcj0IyQIXrUbgeBSFAKVyPwvWoJEDNzMzMzEwsQFK4HoXrUSpAuB6F61F4MUDNzMzMzAw7QK5H4XoUbjBAMzMzMzOzIECkcD0K16MyQD0K16NwvSdAmpmZmZnZPUAAAAAAAAA5QEjhehSuxypA9ihcj8I1MEDD9Shcj4IxQK5H4XoULiVAw/UoXI9CJUAzMzMzMzMjQGZmZmZm5jRAZmZmZmYmMkCPwvUoXM8zQK5H4XoUjkVAAAAAAAAAKkB7FK5H4XopQAAAAAAAACpAZmZmZmZmMEC4HoXrUXgwQIXrUbgehSlACtejcD2KKkDsUbgehSs8QM3MzMzMzClApHA9CtcjPkDXo3A9CtcqQPYoXI/C9S9AhetRuB5FMECuR+F6FC4kQB+F61G4HjZACtejcD3qQUCuR+F6FC47QEjhehSuxzJA", "dtype": "f8"}, "xaxis": "x2", "yaxis": "y2"}, {"bingroup": "x", "histfunc": "sum", "hovertemplate": "sex=Male<br>total_bill=%{x}<br>sum of tip=%{y}<extra></extra>", "legendgroup": "Male", "marker": {"color": "#EF553B", "pattern": {"shape": ""}}, "name": "Male", "orientation": "v", "showlegend": true, "type": "histogram", "x": {"bdata": "rkfhehSuJEDD9ShcjwI1QK5H4XoUrjdACtejcD1KOUAK16NwPYohQOF6FK5H4TpAFK5H4XoULkCPwvUoXI8tQArXo3A9iiRA16NwPQrXLkCuR+F6FG4yQBSuR+F6lDVACtejcD1KMEBmZmZmZqY0QOxRuB6F6zFA9ihcj8K1Q0BSuB6F69EzQI/C9ShczzFAPQrXo3C9KkDhehSuR2EpQDMzMzMzszVAmpmZmZkZI0CamZmZmVkyQEjhehSuxzFAj8L1KFwPOECPwvUoXE8wQHE9CtejsDJAhetRuB5FP0AK16NwPQowQPYoXI/CdTFA4XoUrkfhK0Bcj8L1KFwjQGZmZmZmZj5ACtejcD1KMkB7FK5H4To2QDMzMzMzM0BAzczMzMyMPEAK16NwPQoyQBSuR+F6FClA4XoUrkfhI0CPwvUoXI85QD0K16NwfTNA4XoUrkcBQ0B7FK5H4XomQMP1KFyPIkhACtejcD1KNEAfhetRuJ4rQArXo3A9CiZACtejcD1KMkDXo3A9CpcxQBSuR+F6FDRAexSuR+E6NECF61G4HgUuQArXo3A9CihAhetRuB4FJUDsUbgehesxQDMzMzMzMztAw/UoXI/CNkAK16NwPUoxQHE9CtejcDNAKVyPwvWoMEDXo3A9CldAQPYoXI/C9S9Aj8L1KFwPKkBI4XoUrkcyQPYoXI/CtThAKVyPwvUoNUC4HoXrUfg8QD0K16NwfTZA9ihcj8IVREBI4XoUrkc7QI/C9ShcDyhAw/UoXI8CNUDsUbgehesoQLgehetRuC5APQrXo3B9NED2KFyPwjU5QD0K16NwPTJAAAAAAAAALEApXI/C9QhDQDMzMzMz8zdArkfhehTuPUDhehSuR2EnQIXrUbgehSxAZmZmZmbmL0AK16NwPQohQFK4HoXr0TZAFK5H4XoUM0AAAAAAAAAwQGZmZmZmJkFAuB6F61GYRECPwvUoXI8jQArXo3A9Ch5ApHA9CtcjLEDD9Shcj0IqQMP1KFyPQjFAzczMzMyMOECF61G4HsUzQPYoXI/CFUhAPQrXo3B9MEAAAAAAAIA1QFK4HoXrUSlAH4XrUbieK0CF61G4HoU4QMP1KFyPwjRA9ihcj8K1P0BI4XoUrmdJQB+F61G4ni9AAAAAAAAAHUCamZmZmdk/QFK4HoXr0TBAMzMzMzNzQECkcD0K1+MxQPYoXI/C9SxAcT0K16NQQUAzMzMzM1NBQBSuR+F6VDdAzczMzMysRkDsUbgehSs3QGZmZmZmRkRAcT0K16OwNED2KFyPwnU+QJqZmZmZGTdA4XoUrkdhL0BxPQrXo3A8QPYoXI/C9S5AFK5H4XqUMEA9CtejcD0eQK5H4XoUriRAhetRuB4FK0D2KFyPwrUyQEjhehSuhzRA16NwPQqXOkA9CtejcF1DQIXrUbgeRThAj8L1KFwPPkCkcD0K1+M5QArXo3A9KkhAZmZmZmYmPECuR+F6FC4nQPYoXI/C9R5AUrgehetRKEApXI/C9SghQNejcD0K1ypAMzMzMzNzNECPwvUoXI8qQMP1KFyPAjhA4XoUrkdhL0C4HoXrUTgnQArXo3A9iiVAj8L1KFwPL0CkcD0K1yMkQDMzMzMzMylACtejcD1qQEBI4XoUrgc9QOxRuB6FqzZAUrgehevRMUA=", "dtype": "f8"}, "xaxis": "x", "y": {"bdata": "j8L1KFyP+j8AAAAAAAAMQHsUrkfhegpA16NwPQrXEkAAAAAAAAAAQPYoXI/C9QhAXI/C9Shc/z/Xo3A9CtcJQFyPwvUoXPs/H4XrUbge+T8AAAAAAAAIQFyPwvUoXA9ArkfhehSuDUDNzMzMzMwKQFK4HoXrURBAUrgehetRHkBxPQrXo3AJQLgehetRuAJAAAAAAAAAAEAAAAAAAAAAQDMzMzMzMxFAMzMzMzMz9z8AAAAAAAAEQClcj8L1KApAzczMzMzMDEAAAAAAAAAAQHsUrkfhegJAAAAAAAAAFEDsUbgehesBQFK4HoXrUQRAexSuR+F6CEAfhetRuB71P2ZmZmZmZhZAAAAAAAAACEAAAAAAAAAUQAAAAAAAABhAZmZmZmZmAEAAAAAAAAAIQAAAAAAAAARA9ihcj8L1+D9cj8L1KFwRQBSuR+F6FAxAAAAAAAAACEApXI/C9Sj8P+xRuB6F6xpArkfhehSuCUAAAAAAAAAAQK5H4XoUrv8/FK5H4XoUDkAfhetRuB4FQDMzMzMzMwlAFK5H4XoUAEC4HoXrUbgAQIXrUbgehf8/AAAAAAAA9D+kcD0K16MIQAAAAAAAABBAAAAAAAAACECuR+F6FK4FQAAAAAAAAAhAMzMzMzMzC0AAAAAAAAAUQD0K16NwPQBAAAAAAAAAAEAAAAAAAAAQQGZmZmZmZhdAAAAAAAAACEAAAAAAAAAIQAAAAAAAAAxA7FG4HoXrEkAAAAAAAAAQQAAAAAAAAPg/AAAAAAAACEAAAAAAAAD4Pz0K16NwPfo/PQrXo3A9EEApXI/C9SgRQBSuR+F6FA5AAAAAAAAACEAAAAAAAAAQQGZmZmZmZgRASOF6FK5HFEB7FK5H4XoCQAAAAAAAAARAAAAAAAAAAECuR+F6FK73P3E9CtejcAFAAAAAAAAA+D8AAAAAAAAAQM3MzMzMzBpAAAAAAAAAFECuR+F6FK77PwAAAAAAAABAAAAAAAAABEAAAAAAAAAAQOxRuB6F6wVAAAAAAAAAAEAAAAAAAAAAQAAAAAAAABRAAAAAAAAAAEAAAAAAAAAMQAAAAAAAAARAAAAAAAAAAEDXo3A9CtcLQOxRuB6F6wFAAAAAAAAAEkAAAAAAAAAkQEjhehSuRwlAmpmZmZmZFEBxPQrXo3AJQAAAAAAAABBA4XoUrkfhCEAAAAAAAAAAQAAAAAAAAABAZmZmZmZmDEBxPQrXo3ANQJqZmZmZmRZAAAAAAAAADEAAAAAAAAAaQAAAAAAAAAhAAAAAAAAAFEAAAAAAAAAAQAAAAAAAABBAAAAAAAAA+D97FK5H4XoEQClcj8L1KABAAAAAAAAAEEAK16NwPQr3PwAAAAAAAABAAAAAAAAAAEAAAAAAAAAQQAAAAAAAABBASOF6FK5HC0AAAAAAAAAIQD0K16NwPQBAAAAAAAAAAECkcD0K16MUQAAAAAAAACJAAAAAAAAACEAAAAAAAAD4PwrXo3A9Cvc/mpmZmZmZAUC4HoXrUbj+P0jhehSuR/k/AAAAAAAACEDD9Shcj8IFQAAAAAAAAABAAAAAAAAACEAfhetRuB4LQIXrUbgehfc/AAAAAAAACEAAAAAAAAD0PwAAAAAAAPA/uB6F61G48j+uR+F6FK4XQAAAAAAAAABAAAAAAAAA/D8=", "dtype": "f8"}, "yaxis": "y"}, {"boxpoints": "all", "customdata": [["Male", "No", "Sun", "Dinner", 3], ["Male", "No", "Sun", "Dinner", 3], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 4], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 4], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 4], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 3], ["Male", "No", "Sat", "Dinner", 3], ["Male", "No", "Sat", "Dinner", 2], ["Male", "No", "Sat", "Dinner", 4], ["Male", "No", "Sat", "Dinner", 2], ["Male", "No", "Sat", "Dinner", 4], ["Male", "No", "Sat", "Dinner", 2], ["Male", "No", "Sat", "Dinner", 2], ["Male", "No", "Sat", "Dinner", 2], ["Male", "No", "Sat", "Dinner", 2], ["Male", "No", "Sat", "Dinner", 4], ["Male", "No", "Sat", "Dinner", 2], ["Male", "No", "Sat", "Dinner", 3], ["Male", "No", "Sat", "Dinner", 3], ["Male", "No", "Sat", "Dinner", 3], ["Male", "No", "Sat", "Dinner", 3], ["Male", "No", "Sat", "Dinner", 3], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 4], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 4], ["Male", "No", "Sun", "Dinner", 3], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 4], ["Male", "No", "Sun", "Dinner", 2], ["Male", "Yes", "Sat", "Dinner", 4], ["Male", "Yes", "Sat", "Dinner", 2], ["Male", "No", "Sat", "Dinner", 4], ["Male", "Yes", "Sat", "Dinner", 2], ["Male", "Yes", "Sat", "Dinner", 2], ["Male", "Yes", "Sat", "Dinner", 2], ["Male", "Yes", "Sat", "Dinner", 4], ["Male", "No", "Sat", "Dinner", 3], ["Male", "No", "Sat", "Dinner", 3], ["Male", "No", "Sat", "Dinner", 2], ["Male", "Yes", "Sat", "Dinner", 2], ["Male", "No", "Sat", "Dinner", 2], ["Male", "No", "Sat", "Dinner", 2], ["Male", "Yes", "Sat", "Dinner", 2], ["Male", "No", "<PERSON><PERSON>", "Lunch", 4], ["Male", "No", "<PERSON><PERSON>", "Lunch", 2], ["Male", "No", "<PERSON><PERSON>", "Lunch", 2], ["Male", "Yes", "<PERSON><PERSON>", "Lunch", 2], ["Male", "No", "<PERSON><PERSON>", "Lunch", 2], ["Male", "Yes", "<PERSON><PERSON>", "Lunch", 2], ["Male", "No", "<PERSON><PERSON>", "Lunch", 2], ["Male", "No", "<PERSON><PERSON>", "Lunch", 2], ["Male", "No", "<PERSON><PERSON>", "Lunch", 2], ["Male", "No", "<PERSON><PERSON>", "Lunch", 2], ["Male", "No", "<PERSON><PERSON>", "Lunch", 2], ["Male", "Yes", "<PERSON><PERSON>", "Dinner", 2], ["Male", "No", "<PERSON><PERSON>", "Dinner", 2], ["Male", "Yes", "<PERSON><PERSON>", "Dinner", 4], ["Male", "Yes", "<PERSON><PERSON>", "Dinner", 2], ["Male", "Yes", "<PERSON><PERSON>", "Dinner", 2], ["Male", "Yes", "<PERSON><PERSON>", "Dinner", 2], ["Male", "No", "<PERSON><PERSON>", "Dinner", 2], ["Male", "Yes", "Sat", "Dinner", 2], ["Male", "Yes", "Sat", "Dinner", 2], ["Male", "Yes", "Sat", "Dinner", 2], ["Male", "No", "Sat", "Dinner", 2], ["Male", "No", "Sat", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 3], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 4], ["Male", "No", "<PERSON><PERSON>", "Lunch", 2], ["Male", "No", "<PERSON><PERSON>", "Lunch", 2], ["Male", "No", "<PERSON><PERSON>", "Lunch", 2], ["Male", "No", "<PERSON><PERSON>", "Lunch", 2], ["Male", "No", "<PERSON><PERSON>", "Lunch", 3], ["Male", "No", "<PERSON><PERSON>", "Lunch", 2], ["Male", "Yes", "<PERSON><PERSON>", "Lunch", 2], ["Male", "No", "<PERSON><PERSON>", "Lunch", 6], ["Male", "No", "<PERSON><PERSON>", "Lunch", 5], ["Male", "No", "<PERSON><PERSON>", "Lunch", 2], ["Male", "No", "<PERSON><PERSON>", "Lunch", 2], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 3], ["Male", "No", "Sun", "Dinner", 4], ["Male", "No", "Sun", "Dinner", 4], ["Male", "No", "Sun", "Dinner", 6], ["Male", "No", "Sun", "Dinner", 4], ["Male", "No", "Sun", "Dinner", 4], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 3], ["Male", "No", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 4], ["Male", "Yes", "Sat", "Dinner", 3], ["Male", "Yes", "Sat", "Dinner", 2], ["Male", "Yes", "Sun", "Dinner", 2], ["Male", "Yes", "Sun", "Dinner", 2], ["Male", "Yes", "Sun", "Dinner", 2], ["Male", "Yes", "Sun", "Dinner", 2], ["Male", "Yes", "Sun", "Dinner", 2], ["Male", "Yes", "Sun", "Dinner", 2], ["Male", "Yes", "Sun", "Dinner", 2], ["Male", "Yes", "Sun", "Dinner", 4], ["Male", "Yes", "Sun", "Dinner", 2], ["Male", "Yes", "Sun", "Dinner", 3], ["Male", "Yes", "Sun", "Dinner", 4], ["Male", "Yes", "Sun", "Dinner", 2], ["Male", "No", "Sun", "Dinner", 5], ["Male", "Yes", "Sun", "Dinner", 5], ["Male", "Yes", "Sun", "Dinner", 3], ["Male", "Yes", "Sun", "Dinner", 2], ["Male", "Yes", "<PERSON><PERSON>", "Lunch", 2], ["Male", "Yes", "<PERSON><PERSON>", "Lunch", 2], ["Male", "Yes", "<PERSON><PERSON>", "Lunch", 2], ["Male", "No", "<PERSON><PERSON>", "Lunch", 2], ["Male", "Yes", "<PERSON><PERSON>", "Lunch", 2], ["Male", "Yes", "<PERSON><PERSON>", "Lunch", 2], ["Male", "Yes", "<PERSON><PERSON>", "Lunch", 3], ["Male", "Yes", "<PERSON><PERSON>", "Lunch", 4], ["Male", "Yes", "Sat", "Dinner", 3], ["Male", "Yes", "Sat", "Dinner", 4], ["Male", "Yes", "Sat", "Dinner", 2], ["Male", "Yes", "Sat", "Dinner", 3], ["Male", "Yes", "Sat", "Dinner", 4], ["Male", "No", "Sat", "Dinner", 4], ["Male", "Yes", "Sat", "Dinner", 5], ["Male", "Yes", "Sat", "Dinner", 2], ["Male", "Yes", "Sat", "Dinner", 2], ["Male", "Yes", "<PERSON><PERSON>", "Lunch", 2], ["Male", "Yes", "<PERSON><PERSON>", "Lunch", 1], ["Male", "Yes", "<PERSON><PERSON>", "Lunch", 2], ["Male", "No", "Sat", "Dinner", 4], ["Male", "No", "Sat", "Dinner", 2], ["Male", "Yes", "Sat", "Dinner", 4], ["Male", "Yes", "Sat", "Dinner", 3], ["Male", "No", "Sat", "Dinner", 2], ["Male", "No", "Sat", "Dinner", 2], ["Male", "Yes", "Sat", "Dinner", 2], ["Male", "No", "Sat", "Dinner", 2], ["Male", "Yes", "Sat", "Dinner", 2], ["Male", "Yes", "Sat", "Dinner", 2], ["Male", "No", "Sat", "Dinner", 3], ["Male", "Yes", "Sat", "Dinner", 2], ["Male", "No", "Sat", "Dinner", 2]], "fillcolor": "rgba(255,255,255,0)", "hoveron": "points", "hovertemplate": "sex=%{customdata[0]}<br>total_bill=%{x}<br>smoker=%{customdata[1]}<br>day=%{customdata[2]}<br>time=%{customdata[3]}<br>size=%{customdata[4]}<extra></extra>", "jitter": 0, "legendgroup": "Male", "line": {"color": "rgba(255,255,255,0)"}, "marker": {"color": "#EF553B", "symbol": "line-ns-open"}, "name": "Male", "showlegend": false, "type": "box", "x": {"bdata": "rkfhehSuJEDD9ShcjwI1QK5H4XoUrjdACtejcD1KOUAK16NwPYohQOF6FK5H4TpAFK5H4XoULkCPwvUoXI8tQArXo3A9iiRA16NwPQrXLkCuR+F6FG4yQBSuR+F6lDVACtejcD1KMEBmZmZmZqY0QOxRuB6F6zFA9ihcj8K1Q0BSuB6F69EzQI/C9ShczzFAPQrXo3C9KkDhehSuR2EpQDMzMzMzszVAmpmZmZkZI0CamZmZmVkyQEjhehSuxzFAj8L1KFwPOECPwvUoXE8wQHE9CtejsDJAhetRuB5FP0AK16NwPQowQPYoXI/CdTFA4XoUrkfhK0Bcj8L1KFwjQGZmZmZmZj5ACtejcD1KMkB7FK5H4To2QDMzMzMzM0BAzczMzMyMPEAK16NwPQoyQBSuR+F6FClA4XoUrkfhI0CPwvUoXI85QD0K16NwfTNA4XoUrkcBQ0B7FK5H4XomQMP1KFyPIkhACtejcD1KNEAfhetRuJ4rQArXo3A9CiZACtejcD1KMkDXo3A9CpcxQBSuR+F6FDRAexSuR+E6NECF61G4HgUuQArXo3A9CihAhetRuB4FJUDsUbgehesxQDMzMzMzMztAw/UoXI/CNkAK16NwPUoxQHE9CtejcDNAKVyPwvWoMEDXo3A9CldAQPYoXI/C9S9Aj8L1KFwPKkBI4XoUrkcyQPYoXI/CtThAKVyPwvUoNUC4HoXrUfg8QD0K16NwfTZA9ihcj8IVREBI4XoUrkc7QI/C9ShcDyhAw/UoXI8CNUDsUbgehesoQLgehetRuC5APQrXo3B9NED2KFyPwjU5QD0K16NwPTJAAAAAAAAALEApXI/C9QhDQDMzMzMz8zdArkfhehTuPUDhehSuR2EnQIXrUbgehSxAZmZmZmbmL0AK16NwPQohQFK4HoXr0TZAFK5H4XoUM0AAAAAAAAAwQGZmZmZmJkFAuB6F61GYRECPwvUoXI8jQArXo3A9Ch5ApHA9CtcjLEDD9Shcj0IqQMP1KFyPQjFAzczMzMyMOECF61G4HsUzQPYoXI/CFUhAPQrXo3B9MEAAAAAAAIA1QFK4HoXrUSlAH4XrUbieK0CF61G4HoU4QMP1KFyPwjRA9ihcj8K1P0BI4XoUrmdJQB+F61G4ni9AAAAAAAAAHUCamZmZmdk/QFK4HoXr0TBAMzMzMzNzQECkcD0K1+MxQPYoXI/C9SxAcT0K16NQQUAzMzMzM1NBQBSuR+F6VDdAzczMzMysRkDsUbgehSs3QGZmZmZmRkRAcT0K16OwNED2KFyPwnU+QJqZmZmZGTdA4XoUrkdhL0BxPQrXo3A8QPYoXI/C9S5AFK5H4XqUMEA9CtejcD0eQK5H4XoUriRAhetRuB4FK0D2KFyPwrUyQEjhehSuhzRA16NwPQqXOkA9CtejcF1DQIXrUbgeRThAj8L1KFwPPkCkcD0K1+M5QArXo3A9KkhAZmZmZmYmPECuR+F6FC4nQPYoXI/C9R5AUrgehetRKEApXI/C9SghQNejcD0K1ypAMzMzMzNzNECPwvUoXI8qQMP1KFyPAjhA4XoUrkdhL0C4HoXrUTgnQArXo3A9iiVAj8L1KFwPL0CkcD0K1yMkQDMzMzMzMylACtejcD1qQEBI4XoUrgc9QOxRuB6FqzZAUrgehevRMUA=", "dtype": "f8"}, "xaxis": "x2", "yaxis": "y2"}], "layout": {"barmode": "relative", "legend": {"title": {"text": "sex"}, "tracegroupgap": 0}, "margin": {"t": 60}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermap": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermap"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "xaxis": {"anchor": "y", "domain": [0, 1], "title": {"text": "total_bill"}}, "xaxis2": {"anchor": "y2", "domain": [0, 1], "matches": "x", "showgrid": true, "showticklabels": false}, "yaxis": {"anchor": "x", "domain": [0, 0.7326], "title": {"text": "sum of tip"}}, "yaxis2": {"anchor": "x2", "domain": [0.7426, 1], "matches": "y2", "showgrid": false, "showline": false, "showticklabels": false, "ticks": ""}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["import plotly.express as px\n", "df = px.data.tips()\n", "fig = px.histogram(df, x=\"total_bill\", y=\"tip\", color=\"sex\", marginal=\"rug\",\n", "                    hover_data=df.columns)\n", "fig.show()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ddac93dbfb814700870652258d462129", "version_major": 2, "version_minor": 0}, "text/plain": ["VBox(children=(Figure(axes=[Axis(scale=LinearScale()), Axis(orientation='vertical', scale=LinearScale())], fig…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "size = 100\n", "np.random.seed(0)\n", "x_data = np.arange(size)\n", "y_data = np.cumsum(np.random.randn(size) * 100.0)\n", "from bqplot import pyplot as plt\n", "plt.figure(title=\"My First Plot\")\n", "plt.plot(x_data, y_data)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "<script>\n", "    require.config({\n", "        paths: {\n", "            'echarts':'https://assets.pyecharts.org/assets/v5/echarts.min'\n", "        }\n", "    });\n", "</script>\n", "\n", "        <div id=\"815303693eab488d9bcaff5984a7cc69\" style=\"width:900px; height:500px;\"></div>\n", "\n", "<script>\n", "        require(['echarts'], function(echarts) {\n", "                var chart_815303693eab488d9bcaff5984a7cc69 = echarts.init(\n", "                    document.getElementById('815303693eab488d9bcaff5984a7cc69'), 'white', {renderer: 'canvas'});\n", "                var option_815303693eab488d9bcaff5984a7cc69 = {\n", "    \"animation\": true,\n", "    \"animationThreshold\": 2000,\n", "    \"animationDuration\": 1000,\n", "    \"animationEasing\": \"cubicOut\",\n", "    \"animationDelay\": 0,\n", "    \"animationDurationUpdate\": 300,\n", "    \"animationEasingUpdate\": \"cubicOut\",\n", "    \"animationDelayUpdate\": 0,\n", "    \"aria\": {\n", "        \"enabled\": false\n", "    },\n", "    \"color\": [\n", "        \"#5470c6\",\n", "        \"#91cc75\",\n", "        \"#fac858\",\n", "        \"#ee6666\",\n", "        \"#73c0de\",\n", "        \"#3ba272\",\n", "        \"#fc8452\",\n", "        \"#9a60b4\",\n", "        \"#ea7ccc\"\n", "    ],\n", "    \"series\": [\n", "        {\n", "            \"type\": \"bar\",\n", "            \"name\": \"\\u5546\\u5bb6A\",\n", "            \"legendHoverLink\": true,\n", "            \"data\": [\n", "                114,\n", "                55,\n", "                27,\n", "                101,\n", "                125,\n", "                27,\n", "                105\n", "            ],\n", "            \"realtimeSort\": false,\n", "            \"showBackground\": false,\n", "            \"stackStrategy\": \"samesign\",\n", "            \"cursor\": \"pointer\",\n", "            \"barMinHeight\": 0,\n", "            \"barCategoryGap\": \"20%\",\n", "            \"barGap\": \"30%\",\n", "            \"large\": false,\n", "            \"largeThreshold\": 400,\n", "            \"seriesLayoutBy\": \"column\",\n", "            \"datasetIndex\": 0,\n", "            \"clip\": true,\n", "            \"zlevel\": 0,\n", "            \"z\": 2,\n", "            \"label\": {\n", "                \"show\": true,\n", "                \"margin\": 8,\n", "                \"valueAnimation\": false\n", "            }\n", "        },\n", "        {\n", "            \"type\": \"bar\",\n", "            \"name\": \"\\u5546\\u5bb6B\",\n", "            \"legendHoverLink\": true,\n", "            \"data\": [\n", "                57,\n", "                134,\n", "                137,\n", "                129,\n", "                145,\n", "                60,\n", "                49\n", "            ],\n", "            \"realtimeSort\": false,\n", "            \"showBackground\": false,\n", "            \"stackStrategy\": \"samesign\",\n", "            \"cursor\": \"pointer\",\n", "            \"barMinHeight\": 0,\n", "            \"barCategoryGap\": \"20%\",\n", "            \"barGap\": \"30%\",\n", "            \"large\": false,\n", "            \"largeThreshold\": 400,\n", "            \"seriesLayoutBy\": \"column\",\n", "            \"datasetIndex\": 0,\n", "            \"clip\": true,\n", "            \"zlevel\": 0,\n", "            \"z\": 2,\n", "            \"label\": {\n", "                \"show\": true,\n", "                \"margin\": 8,\n", "                \"valueAnimation\": false\n", "            }\n", "        }\n", "    ],\n", "    \"legend\": [\n", "        {\n", "            \"data\": [\n", "                \"\\u5546\\u5bb6A\",\n", "                \"\\u5546\\u5bb6B\"\n", "            ],\n", "            \"selected\": {},\n", "            \"show\": true,\n", "            \"padding\": 5,\n", "            \"itemGap\": 10,\n", "            \"itemWidth\": 25,\n", "            \"itemHeight\": 14,\n", "            \"backgroundColor\": \"transparent\",\n", "            \"borderColor\": \"#ccc\",\n", "            \"borderRadius\": 0,\n", "            \"pageButtonItemGap\": 5,\n", "            \"pageButtonPosition\": \"end\",\n", "            \"pageFormatter\": \"{current}/{total}\",\n", "            \"pageIconColor\": \"#2f4554\",\n", "            \"pageIconInactiveColor\": \"#aaa\",\n", "            \"pageIconSize\": 15,\n", "            \"animationDurationUpdate\": 800,\n", "            \"selector\": false,\n", "            \"selectorPosition\": \"auto\",\n", "            \"selectorItemGap\": 7,\n", "            \"selectorButtonGap\": 10\n", "        }\n", "    ],\n", "    \"tooltip\": {\n", "        \"show\": true,\n", "        \"trigger\": \"item\",\n", "        \"triggerOn\": \"mousemove|click\",\n", "        \"axisPointer\": {\n", "            \"type\": \"line\"\n", "        },\n", "        \"showContent\": true,\n", "        \"alwaysShowContent\": false,\n", "        \"showDelay\": 0,\n", "        \"hideDelay\": 100,\n", "        \"enterable\": false,\n", "        \"confine\": false,\n", "        \"appendToBody\": false,\n", "        \"transitionDuration\": 0.4,\n", "        \"textStyle\": {\n", "            \"fontSize\": 14\n", "        },\n", "        \"borderWidth\": 0,\n", "        \"padding\": 5,\n", "        \"order\": \"seriesAsc\"\n", "    },\n", "    \"xAxis\": [\n", "        {\n", "            \"show\": true,\n", "            \"scale\": false,\n", "            \"nameLocation\": \"end\",\n", "            \"nameGap\": 15,\n", "            \"gridIndex\": 0,\n", "            \"inverse\": false,\n", "            \"offset\": 0,\n", "            \"splitNumber\": 5,\n", "            \"minInterval\": 0,\n", "            \"splitLine\": {\n", "                \"show\": true,\n", "                \"lineStyle\": {\n", "                    \"show\": true,\n", "                    \"width\": 1,\n", "                    \"opacity\": 1,\n", "                    \"curveness\": 0,\n", "                    \"type\": \"solid\"\n", "                }\n", "            },\n", "            \"animation\": true,\n", "            \"animationThreshold\": 2000,\n", "            \"animationDuration\": 1000,\n", "            \"animationEasing\": \"cubicOut\",\n", "            \"animationDelay\": 0,\n", "            \"animationDurationUpdate\": 300,\n", "            \"animationEasingUpdate\": \"cubicOut\",\n", "            \"animationDelayUpdate\": 0,\n", "            \"data\": [\n", "                \"\\u886c\\u886b\",\n", "                \"\\u6bdb\\u8863\",\n", "                \"\\u9886\\u5e26\",\n", "                \"\\u88e4\\u5b50\",\n", "                \"\\u98ce\\u8863\",\n", "                \"\\u9ad8\\u8ddf\\u978b\",\n", "                \"\\u889c\\u5b50\"\n", "            ]\n", "        }\n", "    ],\n", "    \"yAxis\": [\n", "        {\n", "            \"show\": true,\n", "            \"scale\": false,\n", "            \"nameLocation\": \"end\",\n", "            \"nameGap\": 15,\n", "            \"gridIndex\": 0,\n", "            \"inverse\": false,\n", "            \"offset\": 0,\n", "            \"splitNumber\": 5,\n", "            \"minInterval\": 0,\n", "            \"splitLine\": {\n", "                \"show\": true,\n", "                \"lineStyle\": {\n", "                    \"show\": true,\n", "                    \"width\": 1,\n", "                    \"opacity\": 1,\n", "                    \"curveness\": 0,\n", "                    \"type\": \"solid\"\n", "                }\n", "            },\n", "            \"animation\": true,\n", "            \"animationThreshold\": 2000,\n", "            \"animationDuration\": 1000,\n", "            \"animationEasing\": \"cubicOut\",\n", "            \"animationDelay\": 0,\n", "            \"animationDurationUpdate\": 300,\n", "            \"animationEasingUpdate\": \"cubicOut\",\n", "            \"animationDelayUpdate\": 0\n", "        }\n", "    ],\n", "    \"title\": [\n", "        {\n", "            \"show\": true,\n", "            \"text\": \"\\u67d0\\u5546\\u573a\\u9500\\u552e\\u60c5\\u51b5\",\n", "            \"target\": \"blank\",\n", "            \"subtarget\": \"blank\",\n", "            \"padding\": 5,\n", "            \"itemGap\": 10,\n", "            \"textAlign\": \"auto\",\n", "            \"textVerticalAlign\": \"auto\",\n", "            \"triggerEvent\": false\n", "        }\n", "    ]\n", "};\n", "                chart_815303693eab488d9bcaff5984a7cc69.setOption(option_815303693eab488d9bcaff5984a7cc69);\n", "        });\n", "    </script>\n"], "text/plain": ["<pyecharts.render.display.HTML at 0x11722ce50>"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["from pyecharts.charts import Bar\n", "from pyecharts import options as opts\n", "\n", "bar = (\n", "    Bar()\n", "    .add_xaxis([\"衬衫\", \"毛衣\", \"领带\", \"裤子\", \"风衣\", \"高跟鞋\", \"袜子\"])\n", "    .add_yaxis(\"商家A\", [114, 55, 27, 101, 125, 27, 105])\n", "    .add_yaxis(\"商家B\", [57, 134, 137, 129, 145, 60, 49])\n", "    .set_global_opts(title_opts=opts.TitleOpts(title=\"某商场销售情况\"))\n", ")\n", "# for jupyterlab\n", "# bar.load_javascript()\n", "bar.render_notebook()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["<pyecharts.charts.basic_charts.bar.Bar at 0x12724ec50>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["bar"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "datalayer", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.12"}}, "nbformat": 4, "nbformat_minor": 4}