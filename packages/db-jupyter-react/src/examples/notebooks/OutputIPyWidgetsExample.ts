/*
 * Copyright (c) 2021-2023 Datalayer, Inc.
 *
 * MIT License
 */

export const view = {
  version_major: 2,
  version_minor: 0,
  model_id: '8621699ecc804983a612f09b7dfe806b',
};

export const state = {
  version_major: 2,
  version_minor: 0,
  state: {
    '8621699ecc804983a612f09b7dfe806b': {
      model_name: 'VBoxModel',
      model_module: '@jupyter-widgets/controls',
      model_module_version: '1.0.0',
      state: {
        _view_module_version: '1.0.0',
        children: [
          'IPY_MODEL_a8b1ae50aada4d929397b907115bfc2c',
          'IPY_MODEL_289e54d14b7c4c6d8ac18b4c86ab514c',
          'IPY_MODEL_965bbfeaddd74b9baa488c7f6ac13027',
        ],
        _model_module_version: '1.0.0',
        layout: 'IPY_MODEL_d7d14e74f61f4b3bb5f53a713bcadced',
        _view_count: 1,
      },
    },
    '961d612211fe4c64a70f48942d885c14': {
      model_name: 'LayoutModel',
      model_module: '@jupyter-widgets/base',
      model_module_version: '1.0.0',
      state: {
        _model_module_version: '1.0.0',
        _view_module_version: '1.0.0',
        _view_count: 1,
      },
    },
    '564e75ddea4c4ea0a32c4bd39ed0ed6d': {
      model_name: 'SliderStyleModel',
      model_module: '@jupyter-widgets/controls',
      model_module_version: '1.0.0',
      state: {
        _model_module_version: '1.0.0',
        _view_module_version: '1.0.0',
        _view_count: 1,
      },
    },
    a8b1ae50aada4d929397b907115bfc2c: {
      model_name: 'IntSliderModel',
      model_module: '@jupyter-widgets/controls',
      model_module_version: '1.0.0',
      state: {
        style: 'IPY_MODEL_564e75ddea4c4ea0a32c4bd39ed0ed6d',
        _view_module_version: '1.0.0',
        max: 200,
        value: 100,
        _model_module_version: '1.0.0',
        layout: 'IPY_MODEL_961d612211fe4c64a70f48942d885c14',
        _view_count: 1,
      },
    },
    b63481ca8b7943aa85d097a114a931f5: {
      model_name: 'LayoutModel',
      model_module: '@jupyter-widgets/base',
      model_module_version: '1.0.0',
      state: {
        _model_module_version: '1.0.0',
        _view_module_version: '1.0.0',
        _view_count: 1,
      },
    },
    d86e0cb348eb48bf97f14906a9406731: {
      model_name: 'SliderStyleModel',
      model_module: '@jupyter-widgets/controls',
      model_module_version: '1.0.0',
      state: {
        _model_module_version: '1.0.0',
        _view_module_version: '1.0.0',
        _view_count: 1,
      },
    },
    '289e54d14b7c4c6d8ac18b4c86ab514c': {
      model_name: 'IntSliderModel',
      model_module: '@jupyter-widgets/controls',
      model_module_version: '1.0.0',
      state: {
        style: 'IPY_MODEL_d86e0cb348eb48bf97f14906a9406731',
        _view_module_version: '1.0.0',
        value: 40,
        _model_module_version: '1.0.0',
        layout: 'IPY_MODEL_b63481ca8b7943aa85d097a114a931f5',
        _view_count: 1,
      },
    },
    bb589d8dc365404b94e73a153407128f: {
      model_name: 'LayoutModel',
      model_module: '@jupyter-widgets/base',
      model_module_version: '1.0.0',
      state: {
        _model_module_version: '1.0.0',
        _view_module_version: '1.0.0',
        _view_count: 1,
      },
    },
    '19a24d8ea5a248be8db79790290ae2a1': {
      model_name: 'ButtonStyleModel',
      model_module: '@jupyter-widgets/controls',
      model_module_version: '1.0.0',
      state: {
        _model_module_version: '1.0.0',
        _view_module_version: '1.0.0',
        _view_count: 1,
      },
    },
    '965bbfeaddd74b9baa488c7f6ac13027': {
      model_name: 'ButtonModel',
      model_module: '@jupyter-widgets/controls',
      model_module_version: '1.0.0',
      state: {
        style: 'IPY_MODEL_19a24d8ea5a248be8db79790290ae2a1',
        _view_module_version: '1.0.0',
        icon: 'legal',
        _model_module_version: '1.0.0',
        layout: 'IPY_MODEL_bb589d8dc365404b94e73a153407128f',
        _view_count: 1,
      },
    },
    '6edd9d3360cc47c8aceff0ba11edeca9': {
      model_name: 'DirectionalLinkModel',
      model_module: '@jupyter-widgets/controls',
      model_module_version: '1.0.0',
      state: {
        _model_module_version: '1.0.0',
        target: ['IPY_MODEL_289e54d14b7c4c6d8ac18b4c86ab514c', 'max'],
        source: ['IPY_MODEL_a8b1ae50aada4d929397b907115bfc2c', 'value'],
        _view_module_version: '1.0.0',
        _view_name: '',
      },
    },
    d7d14e74f61f4b3bb5f53a713bcadced: {
      model_name: 'LayoutModel',
      model_module: '@jupyter-widgets/base',
      model_module_version: '1.0.0',
      state: {
        _model_module_version: '1.0.0',
        _view_module_version: '1.0.0',
        _view_count: 1,
      },
    },
  },
};
