{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["2"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["1+1"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"datalayer": {"_about": "Accelerated and Trusted <PERSON><PERSON><PERSON>"}}, "outputs": [{"data": {"text/plain": ["4"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["# I am a Python cell.\n", "print('2+2')"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": []}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["# import micropip\n", "# await micropip.install('ipywidgets')\n", "from ipywidgets import IntSlider\n", "w = IntSlider()\n", "w"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": []}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["w"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"datalayer": {"sql": true}}, "outputs": [{"data": {"text/plain": [""]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["-- I am a SQL cell.\n", "SELECT * FROM LOGS"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python (ipykernel)", "language": "python", "name": "python"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 4}