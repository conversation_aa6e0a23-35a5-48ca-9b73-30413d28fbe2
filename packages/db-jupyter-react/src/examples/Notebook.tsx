/*
 * Copyright (c) 2021-2023 Datalayer, Inc.
 *
 * MIT License
 */

import { createRoot } from 'react-dom/client';
import { useState } from 'react';
import { CellSidebarExtension } from '../components';
import { Notebook } from '../components/notebook/Notebook';
import { JupyterReactTheme } from '../theme';
import nbString from './notebooks/test.ipynb.json';
import sidebar from './extensions/cellsidebars/CellSidebarSource';
import Toolbar from './toolbars/NotebookToolbarAutoSave';
import { ServiceManager } from './service';
// import { ServiceManager } from '@jupyterlab/services';
import {
  createServerSettings,
  DEFAULT_JUPYTER_SERVER_URL,
  DEFAULT_JUPYTER_SERVER_TOKEN,
  ServiceManagerLess,
} from '../jupyter';
import { Jupyter } from '../jupyter/Jupyter';

// const url = DEFAULT_JUPYTER_SERVER_URL;
// const token = DEFAULT_JUPYTER_SERVER_TOKEN;
const url = 'http://localhost:8888';
const token =
  '60c1661cc408f978c309d04157af55c9588ff9557c9380e4fb50785750703da6';

// loadJupyterConfig();
// setJupyterServerUrl(url);
// setJupyterServerToken(token);

const serverSettings = createServerSettings(`${url}`, token);
// const serviceManager1 = new ServiceManager({ serverSettings });
// const serviceManager2 = new ServiceManagerLess();

const div = document.createElement('div');
document.body.appendChild(div);
const root = createRoot(div);
const isServerless = false;

function App() {
  const [serverless, setServerless] = useState(isServerless);
  // const [startDefaultKernel, setStartDefaultKernel] = useState(!isServerless);
  const [serviceManager, setServiceManager] = useState<
    ServiceManager | ServiceManagerLess | undefined
  >(
    isServerless
      ? new ServiceManagerLess()
      : new ServiceManager({ serverSettings })
  );

  const toggleServerless = () => {
    setServerless(!serverless);
    // setStartDefaultKernel(true);
    setServiceManager(
      !serverless
        ? new ServiceManagerLess()
        : new ServiceManager({ serverSettings })
    );
  };

  return (
    <JupyterReactTheme>
      <button onClick={toggleServerless}>
        Toggle Serverless {serverless ? 'on' : 'off'}
      </button>
      <Jupyter
        serverless={serverless}
        serviceManager={serviceManager as any}
        startDefaultKernel={true}
      >
        <Notebook
          cellSidebarMargin={20}
          nbformat={nbString}
          id="notebook-id"
          height="calc(100vh - 2.6rem)" // (Height - Toolbar Height).
          extensions={[
            // new CellSidebarExtension({ factory: CellTitleBar }),
            new CellSidebarExtension(),
          ]}
          Toolbar={Toolbar}
        />
      </Jupyter>
    </JupyterReactTheme>
  );
}

root.render(<App />);
