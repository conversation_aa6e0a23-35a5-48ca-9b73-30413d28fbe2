/*
 * Copyright (c) 2021-2023 Datalayer, Inc.
 *
 * MIT License
 */

.content {
  min-width: 50px;
  min-height: 50px;
  display: block;
  padding: 8px;
  border: 1px solid #c0c0c0;
  border-top: none;
  box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.content > div {
  border: 1px solid #505050;
  overflow: auto;
}

.content input {
  margin: 8px;
}

.red > div {
  background: #e74c3c;
}

.yellow > div {
  background: #f1c40f;
}

.green > div {
  background: #27ae60;
}

.blue > div {
  background: #3498db;
}
