/*
 * Copyright (c) 2021-2023 Datalayer, Inc.
 *
 * MIT License
 */

import { createRoot } from 'react-dom/client';
import { <PERSON><PERSON><PERSON> } from '../jupyter/Jupyter';
import Terminal from '../components/terminal/Terminal';

const div = document.createElement('div');
document.body.appendChild(div);
const root = createRoot(div);

root.render(
  <Jupyter startDefaultKernel={false} terminals>
    <Terminal colormode="dark" height="800px" />
  </Jupyter>
);
