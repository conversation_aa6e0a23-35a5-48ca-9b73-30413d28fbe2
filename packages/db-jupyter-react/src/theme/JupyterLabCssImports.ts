/*
 * Copyright (c) 2021-2023 Datalayer, Inc.
 *
 * MIT License
 */

import '@lumino/widgets/style/index.js';
import '@lumino/dragdrop/style/index.js';

import '@jupyterlab/apputils/style/index.js';
import '@jupyterlab/rendermime/style/index.js';
import '@jupyterlab/codeeditor/style/index.js';
import '@jupyterlab/documentsearch/style/index.js';
import '@jupyterlab/outputarea/style/index.js';
import '@jupyterlab/console/style/index.js';
import '@jupyterlab/completer/style/index.js';
import '@jupyterlab/codemirror/style/index.js';
import '@jupyterlab/codeeditor/style/index.js';
import '@jupyterlab/cells/style/index.js';
import '@jupyterlab/notebook/style/index.js';
import '@jupyterlab/filebrowser/style/index.js';
import '@jupyterlab/terminal/style/index.js';
import '@jupyterlab/theme-light-extension/style/theme.css';
import '@jupyterlab/theme-light-extension/style/variables.css';
import '@jupyterlab/ui-components/style/index.js';

import '@jupyter-widgets/base/css/index.css';
import '@jupyter-widgets/controls/css/widgets-base.css';
