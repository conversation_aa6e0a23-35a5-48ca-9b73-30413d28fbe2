/*
 * Copyright (c) 2021-2023 Datalayer, Inc.
 *
 * MIT License
 */

declare module '*.jpg' {
  const value: any;
  export default value;
}

declare module '*.jpeg' {
  const value: any;
  export default value;
}

declare module '*.png' {
  const value: any;
  export default value;
}

declare module '*.gif' {
  const value: any;
  export default value;
}

declare module '*.svg' {
  const value: any;
  export default value;
}

declare module '*/style/index.js' {
  const value: any;
  export default value;
}

declare module '*/variables.css' {
  const value: any;
  export default value;
}

declare module '*/theme.css' {
  const value: any;
  export default value;
}

declare module '*/base.css' {
  const value: any;
  export default value;
}

declare module '*/index.css' {
  const value: any;
  export default value;
}

declare module '*/widgets-base.css' {
  const value: any;
  export default value;
}

declare module 'jupyterlab-plotly/lib/jupyterlab-plugin' {
  const value: any;
  export default value;
}

declare module 'jupyterlab-plotly/lib/plotly-renderer' {
  const value: any;
  export default value;
}
