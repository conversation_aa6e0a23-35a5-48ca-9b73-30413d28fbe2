/*
 * Copyright (c) 2021-2023 Datalayer, Inc.
 *
 * MIT License
 */

.jp-VarInspector {
  flex-direction: column;
  overflow: auto;
  font-size: var(--jp-ui-font-size1);
}

.jp-VarInspector-table {
  font-family: monospace;
  border-collapse: collapse;
  margin: auto;
  width: 100%;
  color: var(--jp-content-font-color1);
}

.jp-VarInspector-table td,
.jp-VarInspector-table thead {
  border: 1px solid;
  border-color: var(--jp-layout-color2);
  padding: 8px;
}

.jp-VarInspector-table tr:nth-child(even) {
  background-color: var(--jp-layout-color1);
}

.jp-VarInspector-content tr:hover {
  background-color: var(--jp-layout-color2);
}

.jp-VarInspector-table thead {
  font-size: var(--jp-ui-font-size0);
  text-align: center;
  background-color: var(--jp-layout-color2);
  color: var(--jp-ui-font-color1);
  font-family: sans-serif;
  font-weight: 600;
  letter-spacing: 1px;
  text-transform: uppercase;
}

.jp-VarInspector-title {
  font-size: var(--jp-ui-font-size1);
  color: var(--jp-content-font-color1);
  text-align: left;
  padding-left: 10px;
}

.filter-container {
  display: flex;
  flex-direction: column;
}

.filter-search-container {
  display: flex;
  align-items: center;
  padding: 0 1rem;
}

.filter-input {
  width: 20rem !important;
  margin-left: 1rem;
}

.type-button {
  color: var(--jp-ui-font-color0);
}

.name-button {
  color: var(--jp-ui-font-color1);
}

.filter-list {
  display: flex;
}

.jp-VarInspector-table-row-hidden {
  display: none;
}

.jp-VarInspector-deleteButton {
  display: flex;
  justify-content: space-around;
  width: 1em;
}

.jp-VarInspector-inspectButton {
  display: flex;
  justify-content: space-around;
  width: 1em;
}

.jp-VarInspector-varName {
  font-weight: 600;
}

.filter-button-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.icon-button {
  cursor: pointer;
}
