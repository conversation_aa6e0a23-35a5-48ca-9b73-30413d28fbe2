/*
 * Copyright (c) 2021-2023 Datalayer, Inc.
 *
 * MIT License
 */

.dla-KernelInspector-view .dla-KernelInspector-toolbar {
  border-bottom: var(--jp-border-width) solid var(--jp-toolbar-border-color);
  padding: 2px;
  min-height: var(--jp-private-notebook-panel-toolbar-height);
  box-shadow: var(--jp-toolbar-box-shadow);
  background: var(--jp-toolbar-background);
  z-index: 1;
}

.dla-KernelInspector-view .dla-KernelInspector-messagelog {
  padding: 8px;
  overflow: auto;
  white-space: nowrap;
  font-family: var(--jp-code-font-family);
  font-size: var(--jp-code-font-size);
  line-height: var(--jp-code-line-height);
  color: var(--jp-content-font-color1);
  background-color: var(--jp-layout-color0);
  display: grid;
  grid-template-columns: max-content auto;
  grid-gap: 2px 12px;
  align-content: start;
}

.dla-KernelInspector-view .dla-KernelInspector-logheader {
  font-family: var(--jp-ui-font-family);
  font-size: var(--jp-ui-font-size1);
  line-height: 1;
}

.dla-KernelInspector-view .dla-KernelInspector-divider {
  grid-column-end: span 2;
  border-bottom: var(--jp-border-width) solid var(--jp-border-color2);
  padding-top: 2px;
  margin-bottom: 3px;
}

.dla-KernelInspector-view
  .dla-KernelInspector-divider.dla-KernelInspector-logheader {
  border-bottom: var(--jp-border-width) solid var(--jp-border-color0);
}

button.dla-KernelInspector-threadcollapser {
  background-color: transparent;
  border: none;
}

.dla-KernelInspector-icon {
  background-image: var(--jp-icon-json);
}

.dla-KernelInspector-nbtoolbarbutton .jp-ToolbarButtonComponent-label {
  display: none;
}

.kspy-collapser-icon {
  padding: 0;
}

.kspy-collapser-icon svg {
  vertical-align: middle;
}
