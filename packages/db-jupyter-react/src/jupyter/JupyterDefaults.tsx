/*
 * Copyright (c) 2021-2023 Datalayer, Inc.
 *
 * MIT License
 */

export const DEFAULT_JUPYTER_SERVER_URL = 'https://oss.datalayer.run/api/jupyter-server';

export const DEFAULT_JUPYTER_SERVER_TOKEN = '60c1661cc408f978c309d04157af55c9588ff9557c9380e4fb50785750703da6';

/**
 * The URL prefix for the kernel api.
 */
export const DEFAULT_API_KERNEL_PREFIX_URL = '/api/jupyter-server';

export const DEFAULT_KERNEL_NAME = 'python';
