{"$schema": "http://json.schemastore.org/tsconfig", "include": ["./src/**/*", "./src/**/*.json"], "exclude": ["node_modules", "src/examples", "src/jupyter/services/ServiceManagerLite.ts"], "compilerOptions": {"forceConsistentCasingInFileNames": true, "baseUrl": "/", "rootDir": "./src", "allowJs": false, "allowSyntheticDefaultImports": true, "composite": true, "declaration": true, "esModuleInterop": true, "incremental": true, "jsx": "react-jsx", "jsxImportSource": "react", "module": "ES2022", "moduleResolution": "node", "noEmitOnError": true, "noImplicitAny": true, "noUnusedLocals": false, "outDir": "./lib", "preserveWatchOutput": true, "resolveJsonModule": true, "sourceMap": false, "skipLibCheck": true, "strictNullChecks": true, "allowUnusedLabels": true, "target": "ESNext", "types": ["jest", "node"], "lib": ["ESNext", "DOM"]}}