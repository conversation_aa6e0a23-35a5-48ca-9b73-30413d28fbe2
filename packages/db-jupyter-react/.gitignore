# Copyright (c) Datalayer, Inc. https://datalayer.io
# Distributed under the terms of the MIT License.

# Check with git check-ignore -v **/*

# Exclude
.*
dist
build
lib
*.lock
*.sqlite
*.tsbuildinfo
*.egg-info
*-lock.json
*-error.log
*.log
*.lock
*.tsbuildinfo
*-lock.yaml
*.code-workspace
__pycache__
node_modules
coverage
typedoc
storybook-static

**/*.vsix

*.exclude.*

yarn.lock
*.bundle.*
lib/
node_modules/
.eslintcache
.stylelintcache
*.egg-info/
.ipynb_checkpoints
*.tsbuildinfo
**/labextension
# Version file is handled by hatchling
**/jupyter_react/_version.py

**/jupyter_react/static/*.js
**/jupyter_react/static/*.css

# Integration tests
test-results/
playwright-report/

### Python ###
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
.installed.cfg
*.egg
MANIFEST
junit.xml

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# Mr Developer
.mr.developer.cfg
.project
.pydevproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# End of https://www.gitignore.io/api/python

# OSX files
.DS_Store

# Content
packages/*/content

# Static
**/static/*

# VS Code settings
packages/react/.vscode/launch.json

# Include
!**/.*ignore
!**/.*rc
!**/.*rc.js
!**/.*rc.json
!**/.*rc.yml
!**/.*config
!*.*rc.json
!**/.env
!.devcontainer
!.github
!.storybook
!.vscode
!.licenserc.yaml
!dev/notebooks/.datalayer
!docs/static/img
!**/static/README.md
